const { merge } = require("webpack-merge");
const baseConfig = require("./rspack.config.js");
const ForkTsCheckerWebpackPlugin = require("fork-ts-checker-webpack-plugin");

module.exports = (env, argv) => {
  // Check if type checking should be skipped
  const skipTypeCheck = true || process.env.SKIP_TYPECHECK === "true";

  const PORT = process.env.PORT || 9000;
  // Get base configuration
  const baseConfiguration = baseConfig(env, { ...argv, mode: "development" });

  // Create a new configuration with development settings
  const devConfig = merge(baseConfiguration, {
    mode: "development",
    devtool: "inline-source-map",
    devServer: {
      port: PORT,
      hot: true,
      historyApiFallback: true,
      headers: {
        "Access-Control-Allow-Origin": "*",
      },
      proxy: [
        {
          context: ['/api',"/NEW","/auth"],
          target: `http://localhost:9095/` || 'https://test-auth-randyorton.test.pods.kl-infra.com/',
          changeOrigin: true,
          secure: false,
          bypass: function (req) {
            // let webpack handle static assets
            if (req.headers.accept && req.headers.accept.includes("html")) {
              return "/index.html";
            }
            // if (req.url.includes('main.js')) {
            //   return "http://localhost:8082/main.js"; // Let webpack-dev-server handle main.js
            // }
          },
        },
      ],
      client: {
        overlay: {
          errors: true,
          warnings: false,
        },
        
      },
    },
  });

  // If type checking should be skipped, remove the ForkTsCheckerWebpackPlugin
  if (skipTypeCheck) {
    devConfig.plugins = devConfig.plugins.filter(
      (plugin) => !(plugin instanceof ForkTsCheckerWebpackPlugin)
    );
    console.log("TypeScript type checking is disabled for development.");
  } else {
    // In development, we want to make sure the type checker is in async mode
    // to not block the development server
    devConfig.plugins.forEach((plugin) => {
      if (plugin instanceof ForkTsCheckerWebpackPlugin) {
        plugin.options.async = true;
      }
    });
    console.log(
      "TypeScript type checking is running in async mode for development."
    );
  }

  return devConfig;
};
