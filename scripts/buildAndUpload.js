const exec = (command, stdio, cwd = '.') =>
	require('child_process').execSync(command, {
		cwd,
		stdio: stdio || 'inherit',
	});

(function buildAndUpload() {
	console.log(`[BUILD] Building for ${process.env.NODE_ENV}`);

	if (process.env.NODE_ENV.includes('development')) {
		return exec('npm run build');
	}

	const sha = process.env.GIT_SHA		

	console.log(`[BUILD] sha: ${sha}`);

	if (!sha) {
		console.log('Failed to get sha value. Aborting.');
		return;
	}

	let publicUrl = process.env.NODE_ENV.includes('test') || process.env.NODE_ENV === 'production_io'
		? `https://${process.env.CDN_SUBDOMAIN}.klenty.com/moduleFederation/mailbox/${sha}`
		: (process.env.CDN_SUBDOMAIN ? `https://${process.env.CDN_SUBDOMAIN}.klenty.com/moduleFederation/mailbox/${sha.toString().replace(/\n/gim, '')}` : '')
	
	console.log(`[BUILD] PUBLIC_URL: ${publicUrl}`);

	if (!publicUrl) {
		console.log('Failed to get public url value. Aborting.');
		return;
	}

	exec(`PUBLIC_URL=${publicUrl}/ npm run build`);
	exec(`export GIT_SHA=${sha}`);
	exec(`GIT_SHA=${sha} npm run upload`);
})();
