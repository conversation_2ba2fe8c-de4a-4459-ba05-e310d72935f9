/** This file is used to upload the built client to an S3 bucket to be served by the CDN.*/

const AWS = require('aws-sdk');
const path = require('path');
const fs = require('fs');
const util = require('util');

const getStat = util.promisify(fs.stat);
const readDir = util.promisify(fs.readdir);

const exec = (command, cwd) =>
  require('child_process').execSync(command, { cwd });

const config = {
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  region: process.env.AWS_CDN_BUCKET_REGION,
};

const s3 = new AWS.S3(config);

const uploadFile = async (currentPath, directory) => {
  try {
    const stream = fs.createReadStream(path.resolve(currentPath));
    const parameters = {
      Bucket: process.env.NODE_ENV.includes('production')
        ? 'klentyclients'
        : 'klbuildtest',
      Body: stream,
      Key: directory.replace(`${process.cwd()}/build`, "moduleFederation/mailbox/"+ process.env.GIT_SHA),
      ACL: 'public-read',
    };

    if (/(?<=[\w]+)(\.css$)/i.test(directory)) {
      parameters.ContentType = 'text/css; charset=utf-8';
    }
    // need content type for doing gzip in cdn
    if (/(?<=[\w]+)(\.js$)/i.test(currentPath)) {
      parameters.ContentType = 'text/javascript; charset=utf-8';
    }
    await s3.upload(parameters).promise();
    console.log(parameters.Key);
  } catch (e) {
    console.error(`[S3UPLOAD] error while uploading file ${e.message}`);
    console.log(e, e.message);
  }

  return true;
};

const uploadFolder = async (directory) => {
  try {
    const resolvedDir = path.resolve(process.cwd(), directory);
    const dirDetails = await getStat(resolvedDir);
    if (dirDetails.isFile()) {
      uploadFile(resolvedDir, directory);
    } else if (dirDetails.isDirectory()) {
      const contents = await readDir(resolvedDir);
      if (contents && Array.isArray(contents)) {
        await Promise.all(
          contents.map(async (content) => {
            const currentPath = path.resolve(process.cwd(), directory, content);
            await uploadFolder(currentPath);
          })
        );
      }
    }
  } catch (err) {
    console.error(`[S3UPLOAD] error while uploading folder ${err.message}`);
    console.log('err', err.message);
  }
};

(async function uploadToBucket() {
  console.log(`[S3UPLOAD] ${process.env.NODE_ENV}`);
  console.log(
    `[S3UPLOAD] uploading to bucket: ${
      process.env.NODE_ENV.includes('production') ? 'klentydcn' : 'klbuildtest'
    } and folder: ${process.env.GIT_SHA}`
  );

  await uploadFolder('build');

  if (process.env.NODE_ENV.includes('production')) {
    console.log(`[S3UPLOAD] Writing .profile`);
    exec(
      `echo "export GIT_SHA=${
        process.env.GIT_SHA
      };" > ${process.cwd()}/.profile`
    );
  }
})();
