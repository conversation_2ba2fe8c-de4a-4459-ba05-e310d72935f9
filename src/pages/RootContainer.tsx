import React, { useState, useEffect } from "react";
import UserDomainsPage from "./userDomains/UserDomainsPage";
import {
  fetchAllMailboxes,
  MailBoxes,
  MAILBOX_STATUS,
  DOMAIN_STATUS
} from "../service/domains";
import { FlatMailbox } from "./userDomains/AllMailboxesView";
import {  handleErro<PERSON>, Spinner, SPINNER_SIZES } from "@klenty/klenty-ui";
import { CompletedDomainSetup } from "./domains/SetupCompletePage";
import style from "./style.module.css"
import { useNavigate } from "react-router-dom";
import Button from "../components/Button";
import DomainsPage from "./domains";

// Define the DomainDetails interface for UI representation
export interface DomainDetails {
  id: string;
  domainName: string;
  mailboxes: {
    mailboxName: string;
    username: string;
    status: MAILBOX_STATUS;
    emailAddress: string;
    domainName: string;
  }[];
  status: DOMAIN_STATUS;
  date: string;
}

export enum DOMAIN_PAGES {
  NONE,
  EMPTY_DOMAIN ,
  BUY_DOMAINS ,
  DOMAIN_PAGE
}

const RootContainer: React.FC = () => {
  const [currentPage, setCurrentPage] = useState<DOMAIN_PAGES>(DOMAIN_PAGES.NONE);
  const [domains, setDomains] = useState<DomainDetails[]>([]);
  const [mailboxes, setMailboxes] = useState<FlatMailbox[]>([]);
  const [allDomains, setAllDomains] = useState<string[]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    setLoading(true);
    fetchAllMailboxes()
      .then((data) => {
        processMailboxData(data);
      })
      .catch(handleErrors)
      .finally(() => {
        setLoading(false);
      });
  }, []);

  // Process the mailbox data from the API
  const processMailboxData = (data: MailBoxes[]) => {
    const flat: FlatMailbox[] = [];
    const domainsArr: string[] = [];
    const domainDetailsArr: DomainDetails[] = [];

    data.forEach((domain) => {
      domainsArr.push(domain.domain);


      // Build mailboxes for this domain
      const mailboxesForDomain = domain.config.map((cfg) => ({
        mailboxName: `${cfg.username}@${domain.domain}`,
        username: cfg.username,
        status:cfg.status,
        date: domain.date ? new Date(domain.date).toISOString() : "",
        emailAddress: `${cfg.username}@${domain.domain}`,
        domainName: domain.domain,
      }));

      domainDetailsArr.push({
        id: domain.domain, // Use domain name as id if no id
        domainName: domain.domain,
        mailboxes: mailboxesForDomain,
        status: domain.status,
        date: domain.date ? new Date(domain.date).toISOString() : "",
      });

      domain.config.forEach((cfg) => {
        flat.push({
          id: cfg.id,
          username: cfg.username,
          firstName: cfg.firstName,
          lastName: cfg.lastName,
          status: cfg.status,
          errorMsg: cfg.errorMsg,
          domain: domain.domain,
          date: domain.date ? new Date(domain.date).toISOString() : "",
        });
      });
    });

    setMailboxes(flat);
    setAllDomains(domainsArr);
    setDomains([]);
  };


  // Handle new domain data without refetching
  const handleNewDomainPurchase = (
    completedConfigs: CompletedDomainSetup[]
  ) => {
    const newDomainDetails = completedConfigs.map((config) => {
      const mailboxesForDomain = config.mailboxes.map((mb) => ({
        mailboxName: `${mb.username}@${config.domain.name}`,
        username: mb.username,
        status: MAILBOX_STATUS.PENDING ,
        emailAddress: `${mb.username}@${config.domain.name}`,
        domainName: config.domain.name,
      }));

      return {
        id: config.domain.id || config.domain.name,
        domainName: config.domain.name,
        mailboxes: mailboxesForDomain,
        status: DOMAIN_STATUS.PENDING, // New domains start as pending
        date: new Date().toISOString(),
      };
    });

    // Create new flat mailboxes
    const newFlatMailboxes: FlatMailbox[] = [];
    completedConfigs.forEach((config) => {
      config.mailboxes.forEach((mb) => {
        newFlatMailboxes.push({
          id: `${mb.username}@${config.domain.name}`, // Generate a temporary ID
          username: mb.username,
          firstName: mb.firstName,
          lastName: mb.lastName,
          status: MAILBOX_STATUS.PENDING,
          domain: config.domain.name,
          date: new Date().toISOString(),
        });
      });
    });

    // Update state with new domains and mailboxes
    setDomains((prev) => [...prev, ...newDomainDetails]);
    setMailboxes((prev) => [...prev, ...newFlatMailboxes]);
    setAllDomains((prev) => [
      ...prev,
      ...completedConfigs.map((c) => c.domain.name),
    ]);

  };


  if (loading) {
    return (
      <div className={style.center}>
        <Spinner size={SPINNER_SIZES.MEDIUM} />
        </div>
    );
  }

  if(currentPage === DOMAIN_PAGES.BUY_DOMAINS){
    return (
      <DomainsPage
      setCurrentPage={setCurrentPage}
      />
    );
  }

  if (domains.length > 0 || currentPage === DOMAIN_PAGES.DOMAIN_PAGE) {
    return (
      <UserDomainsPage
        domains={domains}
        mailboxes={mailboxes}
        allDomains={allDomains}
        mailboxesLoading={loading}
      />
    );
  } else {
    return (
      <EmptyDomainState  setCurrentPage={setCurrentPage}/>
    );
  }
};

export default RootContainer;



const features = [
  "Build your ICP",
  "Find updated and relevant information about your ICP",
  "Create and execute personalised outreach",
  "Improve your outreach based on realtime signals and learnings",
];

const EmptyDomainState: React.FC<{ setCurrentPage: (page: DOMAIN_PAGES) => void }> = ({ setCurrentPage }) => {
  return (
    <div className={style.emptyDomainContainer}>
      <div className={style.emptyDomainContent}>
        <h1>
          Hi Akshay, <span className={style.gradientText}>Meet SDRx</span>
        </h1>
        <p className={style.emptyDomainSubtitle}>
          Your round the clock sales assistant, to boost your productivity and conversion
        </p>
        <div className={style.emptyDomainFeatures}>
          <span className={style.emptyDomainUse}>Use SDRx for :</span>
          <ul>
            {features.map((feature, idx) => (
              <li key={idx} className={style.emptyDomainFeatureItem}>
                <span className={style.checkmark}>✔</span> {feature}
              </li>
            ))}
          </ul>
        </div>
        <Button
          variant="primary"
          className={style.emptyDomainButton}
          onClick={() => setCurrentPage(DOMAIN_PAGES.BUY_DOMAINS)}
        >
          Buy Domain
        </Button>
      </div>
      <div className={style.emptyDomainImage}>
        {/* Gradient background box */}
        <div className={style.gradientBox}></div>
      </div>
    </div>
  );
};
