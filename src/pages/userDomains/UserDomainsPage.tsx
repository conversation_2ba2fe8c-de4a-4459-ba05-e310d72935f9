import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import styles from "../style.module.css";
import AllMailboxesView from "./AllMailboxesView";
import { FlatMailbox } from "./AllMailboxesView";
import { DomainDetails } from "../RootContainer";
import { DOMAIN_STATUS, domainStatusToString } from "../../service/domains";

// Placeholder Search Icon - replace with actual or Klenty UI icon if available
const SearchIcon = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <circle cx="11" cy="11" r="8"></circle>
    <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
  </svg>
);

interface UserDomainsPageProps {
  domains: DomainDetails[];
  mailboxes: FlatMailbox[];
  allDomains: string[];
  mailboxesLoading: boolean;
}

const UserDomainsPage: React.FC<UserDomainsPageProps> = ({
  domains,
  mailboxes,
  allDomains,
  mailboxesLoading,
}) => {
  const [activeTab, setActiveTab] = useState<
    "domains" | "mailboxes" | "reports" | "warmup"
  >("domains");
  const navigate = useNavigate();
  const [domainSearchTerm, setDomainSearchTerm] = useState("");
  const [domainFilterSelection, setDomainFilterSelection] =
    useState<string>("");

  const uniqueDomainNamesForFilter: string[] = Array.from(
    new Set(domains.map((d) => d.domainName))
  );

  const handlePurchaseNewDomain = () => {
    navigate("/domains/buy", { state: { reset: true } });
  };

  const filteredDomains = domains.filter((domain) => {
    const matchesFilter = domainFilterSelection
      ? domain.domainName === domainFilterSelection
      : true;
    const matchesSearch = domain.domainName
      .toLowerCase()
      .includes(domainSearchTerm.toLowerCase());
    return matchesFilter && matchesSearch;
  });

  const tabs = [
    { key: "domains", label: "Your Domains" },
    { key: "mailboxes", label: "Mailboxes" },
    // { key: 'warmup', label: 'Warmup' },
    // { key: 'reports', label: 'Reports' },
  ];

  return (
    <div className={styles.userDomainsPageContainer}>
      <nav className={styles.verticalNav}>
        {tabs.map((tab) => (
          <button
            key={tab.key}
            className={`${styles.navTabButton} ${
              activeTab === tab.key ? styles.navTabButtonActive : ""
            }`}
            onClick={() => setActiveTab(tab.key as typeof activeTab)}
          >
            {tab.label}
          </button>
        ))}
      </nav>

      <div className={styles.mainContentArea}>
        {activeTab === "domains" && (
          <>
            <header className={styles.domainsPageHeader}>
              <div>
                <h1 className={styles.pageTitle}>Domain</h1>
                <p className={styles.pageSubtitle}>
                  Manage your domains and mailboxes.
                </p>
              </div>
              <button
                className={styles.addMoreButton}
                onClick={handlePurchaseNewDomain}
              >
                + Purchase New Domain
              </button>
            </header>

            <div className={styles.domainListFiltersContainer}>
              <select
                className={styles.domainFilterDropdown}
                value={domainFilterSelection}
                onChange={(e) => setDomainFilterSelection(e.target.value)}
              >
                <option value="">All Domains</option>
                {uniqueDomainNamesForFilter.map((name) => (
                  <option key={name} value={name}>
                    {name}
                  </option>
                ))}
              </select>
              <div className={styles.searchInputWrapperAlt}>
                <span className={styles.searchIconAlt}>
                  <SearchIcon />
                </span>
                <input
                  type="text"
                  placeholder="Search domains..."
                  className={styles.searchInputAlt}
                  value={domainSearchTerm}
                  onChange={(e) => setDomainSearchTerm(e.target.value)}
                />
              </div>
            </div>

            <div className={styles.mailboxDetailsTableContainer}>
              <table className={styles.mailboxDetailsTable}>
                <thead>
                  <tr>
                    {/* <th className={styles.tableTh}><input type="checkbox" /></th> */}
                    <th className={styles.tableTh}>Domain</th>
                    <th className={styles.tableTh}>Status</th>
                    <th className={styles.tableTh}>No. of Mailbox</th>
                    <th className={styles.tableTh}>Created Date</th>
                    {/* <th className={styles.tableTh}>Action</th> */}
                  </tr>
                </thead>
                <tbody>
                  {filteredDomains.map((domain) => (
                    <tr key={domain.id}>
                      {/* <td className={styles.tableTd}><input type="checkbox" /></td> */}
                      <td className={styles.tableTd}>{domain.domainName}</td>
                      <td className={styles.tableTd}>
                        <span
                          className={`${styles.statusBadge} ${
                            domain.status === DOMAIN_STATUS.CONNECTED
                              ? styles.activeStatusBadge
                              : styles.pendingStatusBadge
                          }`}
                        >
                          {domainStatusToString(domain.status)}
                        </span>
                      </td>
                      <td className={styles.tableTd}>
                        {domain.mailboxes.length}
                      </td>
                      <td className={styles.tableTd}>
                        {new Date(domain.date).toLocaleString("en-GB", {
                          day: "2-digit",
                          month: "short",
                          year: "numeric",
                          hour: "2-digit",
                          minute: "2-digit",
                          hour12: true,
                        })}
                      </td>
                      {/* <td className={`${styles.tableTd} ${styles.actionsCell}`}>
                                                <button className={`${styles.actionIcon} ${styles.deleteIcon}`} onClick={() => handleDeleteDomain(domain.id)} title="Delete Domain">
                                                    <DeleteIcon />
                                                </button>
                                            </td> */}
                    </tr>
                  ))}
                  {filteredDomains.length === 0 && (
                    <tr>
                      <td colSpan={6} className={styles.noResultsText}>
                        No domains found matching your criteria.
                        {domains.length > 0 &&
                          domainSearchTerm &&
                          " Try adjusting your search."}
                        {domains.length === 0 &&
                          " You haven't added any domains yet."}
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
            {/* Empty state specifically for when there are no domains at all, and no search term or filter */}
            {domains.length === 0 &&
              !domainSearchTerm &&
              !domainFilterSelection && (
                <div className={styles.emptyStateContainerFullWidth}>
                  <p>You haven't added any domains yet.</p>
                  <button
                    className={styles.primaryButton}
                    onClick={handlePurchaseNewDomain}
                  >
                    + Purchase New Domain
                  </button>
                </div>
              )}
          </>
        )}

        {activeTab === "mailboxes" && (
          <AllMailboxesView
            mailboxes={mailboxes}
            allDomains={allDomains}
            loading={mailboxesLoading}
          />
        )}

        {/* {activeTab === 'warmup' && (
                    <div className={styles.placeholderViewContainer}>
                        <header className={styles.pageHeader}>
                             <h1 className={styles.pageTitle}>Warmup</h1>
                        </header>
                        <p>Warmup View - Coming Soon</p>
                    </div>
                )}

                {activeTab === 'reports' && (
                    <div className={styles.placeholderViewContainer}>
                         <header className={styles.pageHeader}>
                            <h1 className={styles.pageTitle}>Reports</h1>
                        </header>
                        <p>Reports View - Coming Soon</p>
                    </div>
                )} */}
      </div>
    </div>
  );
};

export default UserDomainsPage;
