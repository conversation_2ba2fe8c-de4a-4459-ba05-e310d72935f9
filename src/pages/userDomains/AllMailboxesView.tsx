import React, { useState } from "react";
import {
  Table,
  Button,
  BUTTON_TYPES,
  TextInput,
  Dropdown,
  Spinner,
  SPINNER_SIZES,
} from "@klenty/klenty-ui";
import EditIcon from "../../assets/icons/EditIcon";
import DeleteIcon from "../../assets/icons/DeleteIcon";
import CreateMailboxModal, {
  Domain as ModalDomain,
} from "../../modals/CreateMailboxModal";
import EditMailboxModal from "../../modals/EditMailboxModal";
import { MAILBOX_STATUS, mailboxStatusToString } from "../../service/domains";
import styles from "../style.module.css";
const SearchIcon = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <circle cx="11" cy="11" r="8"></circle>
    <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
  </svg>
);

// Helper to map status code to string


export interface FlatMailbox {
  id?: string;
  username: string;
  assignedUser?: string;
  firstName: string;
  lastName: string;
  status: MAILBOX_STATUS;
  errorMsg?: string;
  domain: string;
  date: string;
}

export interface AllMailboxesViewProps {
  mailboxes: FlatMailbox[];
  allDomains: string[];
  loading: boolean;
}

// const flatMailboxToMailbox = (mb: FlatMailbox): Mailbox => ({
//   id: mb.id || "",
//   mailboxName: mb.username + "@" + mb.domain,
//   username: mb.username,
//   status: statusToString(mb.status),
//   createdOn: mb.createdOn,
//   emailAddress: mb.username + "@" + mb.domain,
//   domainName: mb.domain,
// });

const AllMailboxesView: React.FC<AllMailboxesViewProps> = ({
  mailboxes,
  allDomains,
  loading,
}) => {
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [selectedDomain, setSelectedDomain] = useState<string>("");
  const [isCreateNewMailboxModalOpen, setIsCreateNewMailboxModalOpen] =
    useState(false);

  const modalAvailableDomains: ModalDomain[] = allDomains.map((d, idx) => ({
    id: String(idx),
    name: d,
  }));

  // Modal handlers
  const handleOpenCreateNewMailboxModal = () =>
    setIsCreateNewMailboxModalOpen(true);
  const handleCloseCreateNewMailboxModal = () =>
    setIsCreateNewMailboxModalOpen(false);

  const handleCreateMailboxesSubmit = (selectedDomainIds: string[]) => {
    // In a real implementation, this would call an API to create mailboxes
    console.log("Domains selected for mailbox creation:", selectedDomainIds);
    handleCloseCreateNewMailboxModal();
  };

  const filteredMailboxes = mailboxes.filter((mailbox) => {
    const lowerUser = mailbox.username ? mailbox.username.toLowerCase() : "";
    const lowerSearch = searchTerm.toLowerCase();
    const matchesSearchTerm = lowerUser.includes(lowerSearch);
    const matchesDomain = selectedDomain
      ? mailbox.domain === selectedDomain
      : true;
    return matchesSearchTerm && matchesDomain;
  });

  return (
    <div>
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          gap: 8,
          marginBottom: 24,
        }}
      >
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <div>
            <div style={{ fontWeight: 600, fontSize: 18, marginBottom: 2 }}>
              Mailboxes ({mailboxes.length})
            </div>
            <div style={{ color: "#6B7280", fontSize: 14 }}>
              Create and manage email addresses for your domains
            </div>
          </div>
          <div style={{ minWidth: 140 }}>
            <Button
              type={BUTTON_TYPES.PRIMARY}
              onClick={handleOpenCreateNewMailboxModal}
            >
              + Create Mailbox
            </Button>
          </div>
        </div>
        <div
          style={{
            display: "flex",
            alignItems: "center",
            gap: 16,
            marginTop: 8,
          }}
        >
          <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
            <span style={{ fontSize: 14, color: "#374151", minWidth: 60 }}>
              Domain:
            </span>
            <div>
              <select
                className={styles.domainFilterDropdown}
                value={selectedDomain}
                onChange={(e) => setSelectedDomain(e.target.value)}
              >
                <option value="">All Domains</option>
                {allDomains.map((domain) => (
                  <option key={domain} value={domain}>
                    {domain}
                  </option>
                ))}
              </select>
            </div>
          </div>
          <div style={{ flex: 1 }} />
          <div style={{ minWidth: 240 }}>
            <TextInput
              value={searchTerm}
              onChange={setSearchTerm}
              leftIcon={<SearchIcon />}
              placeholder="Search"
            />
          </div>
        </div>
      </div>
      {/* Table Section */}
      <div>
        {loading ? (
          <div style={{ textAlign: "center", marginTop: 32 }}>
            <Spinner size={SPINNER_SIZES.MEDIUM} />
          </div>
        ) : filteredMailboxes.length === 0 ? (
          <div style={{ textAlign: "center", marginTop: 32 }}>
            No mailboxes found matching your criteria.
            <br />
          </div>
        ) : (
          <div className={styles.mailboxDetailsTableContainer}>
            <table className={styles.mailboxDetailsTable}>
              <thead>
                <tr>
                  {/* <th className={styles.tableTh}><input type="checkbox" /></th> */}
                  <th className={styles.tableTh}>Mailbox</th>
                  <th className={styles.tableTh}>Domain</th>
                  <th className={styles.tableTh}>Status</th>
                  <th className={styles.tableTh}>Created Date</th>
                  {/* <th className={styles.tableTh}>Action</th> */}
                </tr>
              </thead>
              <tbody>
                {filteredMailboxes.map((mailbox) => (
                  <tr key={mailbox.id}>
                    {/* <td className={styles.tableTd}><input type="checkbox" /></td> */}
                    <td className={styles.tableTd}>
                      <div style={{ fontWeight: 600, fontSize: 15 }}>
                        {mailbox.username}@{mailbox.domain}
                      </div>
                      <div
                        style={{ fontSize: 13, color: "#6B7280", marginTop: 2 }}
                      >
                        {mailbox.firstName} {mailbox.lastName}
                      </div>
                    </td>
                    <td className={styles.tableTd}>{mailbox.domain}</td>
                    <td className={styles.tableTd}>
                      <span
                        style={{
                          background:
                            mailbox.status >= MAILBOX_STATUS.ZAP_CONNECTED
                              ? "#D1FAE5"
                              : "#FEF3C7",
                          color:
                            mailbox.status >= MAILBOX_STATUS.ZAP_CONNECTED
                              ? "#059669"
                              : "#B45309",
                          borderRadius: 8,
                          padding: "2px 12px",
                          fontSize: 13,
                          fontWeight: 500,
                          display: "inline-block",
                        }}
                      >
                        {mailboxStatusToString(mailbox.status)}
                      </span>
                    </td>
                    <td className={styles.tableTd}>
                      {new Date(mailbox.date).toLocaleString("en-GB", {
                        day: "2-digit",
                        month: "short",
                        year: "numeric",
                        hour: "2-digit",
                        minute: "2-digit",
                        hour12: true,
                      })}
                    </td>
                    {/* <td className={`${styles.tableTd} ${styles.actionsCell}`} style={{ display: 'flex', gap: 8, alignItems: 'center' }}>
                      <button
                        className={styles.actionIcon}
                        title="Edit Mailbox"
                        onClick={() => handleOpenEditModal(mailbox)}
                        style={{ background: 'none', border: 'none', padding: 4, cursor: 'pointer' }}
                      >
                        <EditIcon />
                      </button>
                      <button
                        className={`${styles.actionIcon} ${styles.deleteIcon}`}
                        onClick={() => handleDeleteMailbox(mailbox.id || '')}
                        title="Delete Mailbox"
                        style={{ background: 'none', border: 'none', padding: 4, cursor: 'pointer' }}
                      >
                        <DeleteIcon />
                      </button>
                    </td> */}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
      {isCreateNewMailboxModalOpen && (
        <CreateMailboxModal
          isOpen={isCreateNewMailboxModalOpen}
          onClose={handleCloseCreateNewMailboxModal}
          availableDomains={modalAvailableDomains}
          onSubmit={handleCreateMailboxesSubmit}
        />
      )}
      {/* {isEditModalOpen && editingMailbox && (
        <EditMailboxModal
          isOpen={isEditModalOpen}
          onClose={handleCloseEditModal}
          mailbox={flatMailboxToMailbox(editingMailbox)}
          onSubmit={handleEditMailboxSubmit}
        />
      )} */}
    </div>
  );
};

export default AllMailboxesView;
