/* style.module.css - Only used classes, converted for CSS modules */
:root {
  --primary-50: #eef2ff;
  --primary-100: #e0e7ff;
  --primary-200: #c7d2fe;
  --primary-300: #a5b4fc;
  --primary-400: #818cf8;
  --primary-500: #6366f1;
  --primary-600: #4f46e5;
  --primary-700: #4338ca;
  --primary-800: #3730a3;
  --primary-900: #312e81;
  --secondary-50: #fff1f2;
  --secondary-100: #ffe4e6;
  --secondary-200: #fecdd3;
  --secondary-300: #fda4af;
  --secondary-400: #fb7185;
  --secondary-500: #f43f5e;
  --secondary-600: #e11d48;
  --secondary-700: #be123c;
  --secondary-800: #9f1239;
  --secondary-900: #881337;
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  --amber-50: #fffbeb;
  --amber-100: #fef3c7;
  --amber-500: #f59e0b;
  --green-100: #dcfce7;
  --green-600: #16a34a;
  --green-800: #166534;
  --red-100: #fee2e2;
  --red-800: #991b1b;
  --blue-50: #f0f9ff;
  --blue-100: #e0f2fe;
  --blue-600: #0284c7;
  --purple-100: #f3e8ff;
  --pink-100: #fce7f3;
  --cyan-100: #cffafe;
  --indigo-100: #e0e7ff;
  --lime-100: #ecfccb;
  --orange-100: #ffedd5;

  /* Variables from CreateMailboxModal */
  --modal-overlay-bg: rgba(55, 65, 81, 0.6);
  /* Darker overlay */
  --modal-bg: #ffffff;
  --modal-border-radius: 8px;
  --modal-box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --modal-padding: 24px;
  --modal-header-padding: 20px 24px;
  --modal-body-padding: 20px 24px;
  --modal-footer-padding: 20px 24px;
  --modal-title-color: #111827;
  /* gray-900 */
  --modal-subtitle-color: #4B5563;
  /* gray-600 */
  --close-button-color: #6B7280;
  /* gray-500 */
  --close-button-hover-color: #1F2937;
  /* gray-800 */
  --button-primary-bg: #4F46E5;
  /* primary-600, also used by modal submit */
  --button-primary-hover-bg: #4338CA;
  /* primary-700, also used by modal submit hover */
  --button-secondary-bg: #E5E7EB;
  /* gray-200 */
  --button-secondary-hover-bg: #D1D5DB;
  /* gray-300 */
  --button-secondary-text: #374151;
  /* gray-700 */
  --button-border-radius: 6px;
  /* Consistent button border radius */
  --klenty-purple: #6366F1;
  /* Klenty primary color for modal submit and dropdown */
  --klenty-light-purple: #E0E7FF;
  /* For dropdown selected option bg and focus shadow */
}

.domainsPage {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.stepsContainer {
  display: flex;
  justify-content: center;
  margin: 30px auto 40px;
  position: relative;
  max-width: 400px;
  width: 90%;
}

.stepsLine {
  position: absolute;
  top: 24px;
  left: 50%;
  transform: translateX(-50%);
  height: 2px;
  background-color: var(--gray-300);
  width: 70%;
  z-index: 1;
}

.step {
  width: 33.33%;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
}

.stepCircle {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: var(--gray-200);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  margin-bottom: 12px;
  color: var(--gray-600);
}

.stepCircleActive {
  background-color: var(--primary-500);
  color: white;
}

.stepLabel {
  font-size: 14px;
  color: var(--gray-600);
  text-align: center;
}

/* Neutralize .mailboxContainer as .domainSearch is now the card -- NO, mailboxContainer IS THE CARD NOW*/
.mailboxContainer {
  /* width: 100%; */
  /* Removed: max-width and margin auto will handle width */
  background-color: white;
  border: 1px solid var(--gray-200);
  border-radius: 8px;
  padding: 32px 48px;
  /* Internal padding for the card content */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  margin-top: 24px;
  /* Added margin-top to space from step indicator */
  margin-bottom: 32px;
  max-width: 1000px;
  margin-left: auto;
  margin-right: auto;
}

.mailboxHeader {
  margin-bottom: 16px;
  text-align: center;
}

.mailboxHeaderH2 {
  font-size: 26px;
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: 12px;
}

.mailboxDescription {
  font-size: 16px;
  color: var(--gray-700);
  line-height: 1.6;
  margin-bottom: 24px;
  text-align: center;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
}

/* Updated Mailbox Tips Section Styles */
.mailboxTips {
  background-color: var(--blue-50);
  /* Retain light blue background */
  border: 1px solid var(--blue-100);
  /* Retain light blue border */
  border-radius: 8px;
  padding: 24px;
  /* Increase padding for more whitespace */
  margin-bottom: 32px;
  /* Increase bottom margin */
}

.tipTitle {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  /* Increased margin */
  color: var(--blue-700);
  /* Title text color from icon */
}

.tipTitleIcon {
  color: var(--blue-700);
  /* Ensure icon color matches */
  margin-right: 10px;
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.tipTitleText {
  font-size: 16px;
  font-weight: 500;
  /* Retain font weight */
  /* color: var(--blue-700); Already set on .tipTitle */
}

.tipList {
  list-style: none;
  /* Keep list style as none, bullets are custom */
  padding: 0;
  /* Remove all padding */
  margin: 16px 0 0 0;
  /* Add margin top to space from title, reset other margins */
  /* padding-left: 30px; /* Removed: Indent list to align with title text (icon width 20px + icon margin-right 10px) */
}

/* New style for the icon within each list item */
.listItemInfoIcon {
  color: var(--primary-500);
  /* Matches h4 color in infoBanner based on user changes */
  margin-right: 10px;
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  margin-top: 3px;
  /* Adjust for vertical alignment with the first line of text */
}

.tipItem {
  display: flex;
  /* Keep display flex for bullet alignment */
  margin-bottom: 4px;
  /* Reduced spacing between items */
  align-items: flex-start;
  /* Align items to the start for multi-line text */
}

.center {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tipItem:last-child {
  margin-bottom: 0;
}

.tipBullet {
  width: 5px;
  /* Slightly smaller bullet */
  height: 5px;
  background-color: var(--blue-600);
  /* Darker blue bullet */
  border-radius: 50%;
  margin-right: 12px;
  margin-top: 7px;
  /* Adjust for vertical alignment with text */
  flex-shrink: 0;
}

.tipContent {
  flex: 1;
}

.tipContentHeading {
  color: var(--blue-700);
  /* Blue color for heading */
  font-weight: 600;
  /* Retain font weight */
  font-size: 15px;
  display: inline;
  /* Make heading inline with bullet if structure supports, or adjust parent */
  margin-bottom: 4px;
  /* Reduced margin as it's followed by p tag */
}

.tipContentText {
  color: var(--gray-700);
  font-size: 14px;
  line-height: 1.6;
  /* If .tipContentHeading is inline, this p tag will naturally flow after it.
     If .tipContentHeading is block, this will be on a new line, adjust margin-top if needed */
  margin-top: 4px;
  /* Add a small margin if heading is block, for spacing */
}

.domainSearchForm {
  width: 100%;
}

.searchContainer {
  position: relative;
  margin-bottom: 20px;
  z-index: 10;
}

.searchInputWrapper {
  display: flex;
  width: 100%;
  position: relative;
  margin-bottom: 16px;
  gap: 8px;
  /* Add gap between input and button */
}

.searchIcon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray-400);
  z-index: 5;
}

.searchInput {
  flex: 1;
  padding: 12px 12px 12px 40px;
  font-size: 15px;
  border: 1px solid var(--gray-300);
  /* border-radius: 6px 0 0 6px; */
  /* Remove specific corner radii */
  border-radius: 6px;
  /* Apply uniform border radius */
  transition: all 0.2s;
}

.searchInputFocus {
  outline: none;
  border-color: var(--primary-400);
  box-shadow: 0 0 0 2px var(--primary-100);
}

.searchButton {
  min-width: 150px;
  /* Adjusted min-width for "Check Availability" */
  padding: 0 16px;
  border: none;
  background-color: var(--primary-600);
  color: white;
  font-weight: 500;
  font-size: 14px;
  /* border-radius: 0 6px 6px 0; */
  /* Remove specific corner radii */
  border-radius: 6px;
  /* Apply uniform border radius */
  cursor: pointer;
  transition: background-color 0.2s;
}

.searchButtonHover {
  background-color: var(--primary-700);
}

.searchButtonDisabled {
  background-color: var(--gray-400);
  cursor: not-allowed;
}

.domainCartCounter {
  display: flex;
  justify-content: flex-end;
  color: var(--gray-600);
  font-size: 14px;
  margin-bottom: 16px;
  padding-right: 8px;
}

/* Updated Dropdown Container Styles */
.domainDropdown {
  position: absolute;
  top: calc(100% + 4px);
  left: 0;
  right: 0;
  width: 100%;
  /* Light blueish background from image */
  border-radius: 8px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  /* Softer shadow */
  z-index: 1000;
  /* overflow: hidden; Keep this to respect border-radius for children - will be handled by children */
  border: 1px solid var(--primary-200);
  /* Light purple border for the container */
  padding: 16px;
  /* Overall padding for the container */
  animation: dropdownFadeIn 0.2s ease forwards;
  max-height: 60vh;
  /* Limit height relative to viewport */
  display: flex;
  /* Use flexbox for internal layout */
  flex-direction: column;
  /* Stack children vertically */
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Remove old header style if not needed, or adapt if there's a new header design */
/* .domainDropdownHeader { display: grid; grid-template-columns: 1.5fr 1fr 1fr; padding: 12px 16px; background-color: var(--gray-50); border-bottom: 1px solid var(--gray-200); } */
/* .domainDropdownHeaderSpan { font-size: 13px; font-weight: 600; color: var(--gray-600); } */

.domainDropdownList {
  /* max-height: 350px; /* Or adjust as needed */
  /* Remove fixed max-height */
  overflow-y: auto;
  margin: 0 -4px;
  padding: 0 4px;
  flex-grow: 1;
  /* Allow list to take available space */
  min-height: 0;
  /* Important for scroll in flex item */
}

.domainDropdownList::-webkit-scrollbar {
  width: 6px;
  /* Thinner scrollbar */
}

.domainDropdownList::-webkit-scrollbar-track {
  background: transparent;
  /* Make track transparent */
  border-radius: 3px;
  margin: 2px;
  /* Margin for the track itself */
}

.domainDropdownList::-webkit-scrollbar-thumb {
  background-color: var(--gray-300);
  /* Color of the scrollbar thumb */
  border-radius: 3px;
  /* border: 2px solid var(--primary-100); /* Creates padding around thumb - removed for thinner look */
}

.domainDropdownList::-webkit-scrollbar-thumb:hover {
  background-color: var(--gray-400);
  /* Darker thumb on hover */
}

.noDomainsMessage {
  padding: 24px 16px;
  text-align: center;
  color: var(--gray-600);
  /* Slightly darker for better readability on light blue bg */
  background-color: white;
  /* Give it a card look if it's the only thing */
  border-radius: 8px;
  margin: 8px 0;
}

.noDomainsMessageP {
  margin: 4px 0;
  font-size: 14px;
}

.searchingMessage {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px 16px;
  text-align: center;
  color: var(--gray-500);
}

.miniCart {
  background-color: white;
  border: 1px solid var(--primary-200);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  margin: 16px 0 24px;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.miniCartHeader {
  padding: 12px 16px;
  background-color: var(--primary-50);
  border-bottom: 1px solid var(--primary-100);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.miniCartHeaderH3 {
  font-size: 15px;
  font-weight: 500;
  color: var(--gray-800);
  margin: 0;
}

.domainCount {
  font-size: 13px;
  color: var(--primary-700);
  background-color: var(--primary-100);
  padding: 3px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.miniCartItems {
  padding: 10px;
}

.miniCartItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 10px;
  border-radius: 6px;
  margin-bottom: 6px;
  transition: background-color 0.2s;
}

.miniCartItemHover {
  background-color: var(--gray-50);
}

.miniCartItemLastChild {
  margin-bottom: 0;
}

.miniCartItemDomainName {
  font-size: 14px;
  font-weight: 500;
  color: var(--gray-800);
}

.miniRemoveButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 22px;
  height: 22px;
  color: var(--gray-500);
  background: transparent;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s;
  padding: 0;
}

.miniRemoveButtonHover {
  color: var(--red-800);
  background-color: var(--red-100);
}

.checkoutButton {
  width: calc(100% - 20px);
  margin: 4px 10px 12px;
  padding: 8px 0;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  background-color: var(--primary-600);
  color: white;
  cursor: pointer;
  transition: all 0.2s;
}

.checkoutButtonHover {
  background-color: var(--primary-700);
}

.domainSearchActions {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.clearSearchBtn {
  padding: 10px 16px;
  background-color: white;
  color: var(--gray-700);
  border: 1px solid var(--gray-300);
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}

.continueBtn {
  padding: 10px 16px;
  background-color: var(--primary-600);
  /* Primary action color for enabled state */
  color: white;
  /* White text for enabled state */
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.continueBtn:hover:not(:disabled) {
  background-color: var(--primary-700);
  /* Darker shade on hover for enabled state */
}

.continueBtn:disabled {
  background-color: var(--gray-200);
  /* Light gray background for disabled button appearance */
  color: var(--gray-500);
  /* Subtle gray text for disabled state */
  cursor: not-allowed;
  border: 1px solid var(--gray-300);
  /* Optional: add a light border to enhance button shape */
}

/* Updated Styles for New Domain Dropdown Items */
.domainDropdownItemNew {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #FFFFFF;
  /* White background for cards inside the blueish dropdown */
  padding: 6px 16px;
  /* Changed from 16px */
  border-radius: 6px;
  /* Slightly less rounded corners for a flatter card look */
  border: 1px solid var(--primary-100);
  /* Lighter purple border */
  margin-bottom: 12px;
  /* Increased spacing between items */
  transition: box-shadow 0.2s ease-in-out, border-color 0.2s ease-in-out;
  min-height: 44px;
  /* Ensure consistent row height */
}

.domainDropdownItemNew:last-child {
  margin-bottom: 0;
}

.domainDropdownItemNew:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  /* Enhanced hover shadow */
  border-color: var(--primary-300);
  /* Highlight border on hover */
}

.domainInfoGroup {
  display: flex;
  align-items: center;
  flex-grow: 1;
  gap: 12px;
  /* Slightly increased gap */
  min-width: 0;
}

.domainNameTextNew {
  font-size: 15px;
  /* Keep font size */
  font-weight: 500;
  /* Keep font weight */
  color: var(--gray-900);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.availabilityTagNew {
  background-color: var(--green-100);
  /* Existing light blueish purple */
  color: var(--green-800);
  /* Existing darker purple text */
  padding: 5px 12px;
  /* Slightly more padding */
  border-radius: 16px;
  /* More pill-like */
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  flex-shrink: 0;
}

.addToCartBtnNew {
  background-color: transparent;
  /* Changed from var(--primary-600) */
  color: var(--primary-600);
  /* Changed from white */
  border: none;
  border-radius: 6px;
  padding: 10px 8px;
  /* Adjusted padding */
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  /* gap: 8px; */
  /* Removed gap as icon is gone */
  cursor: pointer;
  transition: color 0.2s ease, text-decoration 0.2s ease;
  /* Adjusted transition */
  white-space: nowrap;
  flex-shrink: 0;
}

.addToCartBtnNew:hover:not(:disabled) {
  /* background-color: var(--primary-700); */
  /* Removed background change */
  color: var(--primary-700);
  /* Darker purple on hover */
  text-decoration: underline;
  /* Add underline on hover */
}

.addToCartBtnNew:disabled,
.addedToCartBtnNew:disabled {
  /* Keep this for the disabled state if the new structure is also a button that can be disabled */
  opacity: 0.7;
  cursor: not-allowed;
}

/* Styles for the new "Added" state with Remove option */
.domainAddedStateWrapper {
  display: flex;
  align-items: center;
  /* justify-content: space-between; */
  /* Removed to bring items closer */
  gap: 8px;
  /* Reduced from 12px */
  padding: 10px 0px;
  /* Adjusted padding, similar to .addToCartBtnNew's vertical padding but no horizontal for wrapper */
  /* width: 100%; */
  /* Removed to allow shrink-to-fit */
  box-sizing: border-box;
}

.addedStatusText {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  font-weight: 500;
  color: var(--primary-600);
  /* Purple text for "Added" */
}

.addedTickIconWrapper {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: var(--green-100);
  /* Light green background for the icon wrapper */
  border-radius: 50%;
  padding: 3px;
  /* Small padding around the icon */
  line-height: 0;
  /* Helps with alignment of icon if it has intrinsic line-height */
}

.addedStatusText svg {
  width: 14px;
  height: 14px;
  stroke: var(--green-600);
  /* Green tick path */
  display: block;
  /* Helps with alignment */
}

.removeDomainLink {
  background: transparent;
  border: none;
  color: var(--gray-500);
  text-decoration: underline;
  font-size: 14px;
  font-weight: 500;
  /* Match other link/button styles */
  cursor: pointer;
  padding: 0;
  transition: color 0.2s ease;
}

.removeDomainLink:hover {
  color: var(--gray-700);
}

.domainUnavailableTextNew {
  color: var(--gray-500);
  font-size: 14px;
  /* Consistent font size */
  font-style: italic;
  /* Italicized as per image */
  margin-left: auto;
  padding: 10px 16px;
  /* Consistent padding with button */
  white-space: nowrap;
  flex-shrink: 0;
}

/* Updated Styles for Selection Limit Hints */
.selectionLimitHint,
.selectionLimitReachedHint {
  padding: 10px 16px;
  /* Standardized padding */
  margin: 0 0 12px 0;
  /* Margin below hint, before list starts */
  font-size: 14px;
  /* Slightly larger font */
  text-align: center;
  border-radius: 6px;
  /* Consistent border radius */
  flex-shrink: 0;
  /* Prevent hints from shrinking */
}

.selectionLimitHint {
  /* Darker text for better readability */
  background-color: var(--primary-100);
  /* Lighter blue, distinct from primary-50 */
  /* border: 1px solid var(--blue-200); Optional: if a border is desired */
}

.selectionLimitReachedHint {
  font-weight: 500;
  color: var(--primary-700);
  background-color: var(--primary-100);
  /* Existing style, looks good */
  /* border: 1px solid var(--primary-200); Optional */
}

/* Styles from DomainCart (now merged) */

.miniCart {
  background-color: white;
  border: 1px solid var(--primary-200);
  /* Light purple border */
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  margin: 16px 0 24px;
  animation: slideIn 0.3s ease-out;
  /* Keep existing animation */
}

/* @keyframes slideIn is already defined globally or should be if not. */
/* For CSS modules, if it was in DomainCart.module.css, it would be local. */
/* Assuming slideIn is a global animation or already defined in style.module.css */

.miniCartHeader {
  padding: 12px 16px;
  background-color: var(--primary-50);
  /* Lightest purple background */
  border-bottom: 1px solid var(--primary-100);
  display: flex;
  /* To align title and badge */
  justify-content: space-between;
  /* To align title and badge */
  align-items: center;
  /* To align title and badge */
}

.miniCartHeader h3 {
  font-size: 15px;
  font-weight: 500;
  color: var(--gray-800);
  margin: 0;
}

.domainCountBadge {
  font-size: 13px;
  color: var(--primary-700);
  background-color: var(--primary-100);
  padding: 4px 10px;
  border-radius: 12px;
  font-weight: 500;
}

.selectedDomainBubbleContainer {
  padding: 12px 16px;
  /* Padding for the area containing bubbles */
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  /* Spacing between bubbles */
}

.domainBubble {
  display: inline-flex;
  align-items: center;
  padding: 6px 10px;
  background-color: var(--gray-100);
  border: 1px solid var(--gray-200);
  border-radius: 16px;
  /* Pill shape */
  font-size: 14px;
  color: var(--gray-800);
  transition: background-color 0.2s ease;
}

.domainBubbleName {
  margin-right: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
  /* Prevent very long names from breaking layout */
}

.removeDomainBubbleBtn {
  background: transparent;
  border: none;
  color: var(--gray-500);
  cursor: pointer;
  padding: 2px;
  /* Minimal padding for click area */
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: color 0.2s ease, background-color 0.2s ease;
}

.removeDomainBubbleBtn svg {
  width: 14px;
  /* Match SVG size in JSX */
  height: 14px;
}

.removeDomainBubbleBtn:hover {
  color: var(--red-700);
  background-color: var(--red-100);
}

.selectedDomainName {
  font-size: 16px;
  font-weight: 500;
  color: var(--primary-700);
}

.formSectionHeader {
  display: flex;
  align-items: baseline;
  gap: 4px;
  /* Overall margin below the header group */
  /* Space above the decorative line */
  position: relative;
  /* For pseudo-element positioning */
}

.formSectionHeader h3 {
  font-size: 16px;
  font-weight: 500;
  color: var(--gray-800);
  margin: 0;
  /* Reset margin for flex alignment */
  /* padding-bottom: 16px; */
  /* Removed */
  /* border-bottom: 1px solid var(--gray-200); */
  /* Removed */
}

/* Styles migrated from style.css for CreateMailboxes.tsx */

/* Info Banner */
.infoBanner {
  display: flex;
  align-items: flex-start;
  background-color: var(--primary-50);
  border: 1px solid var(--primary-200);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 32px;
  color: var(--blue-800);
}

.infoIcon {
  /* Was .info-banner .info-icon */
  color: var(--blue-500);
  margin-right: 12px;
  flex-shrink: 0;
  margin-top: 2px;
  /* Slight adjustment for alignment */
}

.infoBannerContent h4 {
  /* Descendant selector */
  font-size: 15px;
  font-weight: 600;
  color: var(--primary-500);
  margin: 0 0 8px 0;
}

.infoBannerContent p {
  /* Descendant selector */
  font-size: 14px;
  color: var(--blue-700);
  margin: 0 0 4px 0;
  line-height: 2;
}

.infoBannerContent p:last-child {
  /* Descendant selector */
  margin-bottom: 0;
}

/* Form Container */
.mailboxFormContainer {
  background-color: white;
  border: 1px solid var(--gray-200);
  border-radius: 8px;
  padding: 24px 32px 32px 32px;
  /* More padding */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* .mailboxFormContainer h3 is already handled by .formSectionHeader h3 */

/* Form Elements */
.formRow {
  display: flex;
  gap: 24px;
  /* Spacing between first and last name */
  margin-bottom: 20px;
}

.formGroup {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
  flex-grow: 1;
  /* Make groups grow equally */
}

.formRow>.formGroup {
  /* Descendant combinator for items in a row */
  margin-bottom: 0;
}

.formGroup label {
  /* Descendant selector */
  font-size: 14px;
  font-weight: 500;
  color: var(--gray-700);
  margin-bottom: 6px;
}

.requiredAsterisk {
  color: var(--secondary-500);
  /* Reddish color for asterisk */
  margin-left: 2px;
}

.formInput,
/* Generic class for text inputs, can be applied directly */
.formGroup input[type="text"] {
  /* More specific for inputs within formGroup */
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--gray-300);
  border-radius: 6px;
  font-size: 14px;
  line-height: 1.5;
  background-color: white;
  color: var(--gray-800);
  transition: border-color 0.2s, box-shadow 0.2s;
}

.formInput:focus,
.formGroup input[type="text"]:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 2px var(--primary-100);
}

/* Input with button - if used, the internal input might need specific styling */
.inputWithButton {
  position: relative;
  display: flex;
  align-items: center;
}

.inputWithButton input {
  /* Assuming this targets an input directly within .inputWithButton */
  padding-right: 40px;
  /* Make space for a button not currently in JSX */
  flex-grow: 1;
}

/* Form Actions */
.formActions {
  display: flex;
  justify-content: flex-end;
  /* Align button to the right */
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid var(--gray-200);
}

/* Base button styles */
.btn {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  text-decoration: none;
  /* Ensure no underline for button-like links */
  display: inline-block;
  /* For proper padding and layout */
  line-height: normal;
  /* Reset line-height */
}

.btnPrimary {
  /* Modifier for primary button */
  background-color: var(--primary-600);
  color: white;
  border-color: var(--primary-600);
}

.btnPrimary:hover {
  background-color: var(--primary-700);
  color: white;
  /* Ensure text color remains white on hover */
}

/* Tab Styles for CreateMailboxesPage */
.tabContainer {
  display: flex;
  border-radius: 6px;
  padding: 4px;
  margin-bottom: 24px;
  border: 1px solid var(--gray-200);
}

.tabButton {
  flex: 1;
  padding: 12px 4px;
  /* Adjusted padding slightly for a tighter look if desired */
  cursor: pointer;
  border: none;
  background-color: transparent;
  font-size: 14px;
  font-weight: 500;
  color: var(--gray-600);
  margin-right: 8px;
  transition: color 0.2s, border-color 0.2s;
}

.tabActive {
  color: var(--primary-600);
  /* Active color, e.g., #6366f1 */
  font-weight: 500;
  /* Or 600 if a bit bolder is desired */
  background-color: var(--primary-100);
  border: 1px solid var(--primary-200);
  border-radius: 6px;
}

.autoGeneratePlaceholder {
  padding: 20px;
  text-align: center;
  color: var(--gray-500);
  min-height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px dashed var(--gray-300);
  border-radius: 6px;
}

/* Styles for Multi-Mailbox Entry */
.manualMailboxEntryArea {
  display: flex;
  flex-direction: column;
  gap: 12px;
  /* Space between each entry row */
  /* Space before form actions */
}

.mailboxEntryRow {
  display: flex;
  /* align-items: center; */
  /* Changed from center */
  align-items: flex-start;
  /* Align items to the top */
  gap: 12px;
}

.mailboxRowNumber {
  color: var(--gray-500);
  font-size: 14px;
  font-weight: 500;
  width: 20px;
  /* Fixed width for alignment */
  text-align: right;
  /* Align numbers to the right for a neat column */
  flex-shrink: 0;
  /* Prevent shrinking */
  line-height: 36px;
  /* Approximate height of an input field */
}

.formInputGroup {
  /* Wrapper for each input in the row */
  flex: 1;
  /* Allow input groups to share space */
  min-width: 0;
  /* Prevent overflow issues in flex items */
}

/* .formInput class is already defined and should be sufficient for the inputs themselves */

.mailboxDomainSuffix {
  color: var(--gray-700);
  font-size: 14px;
  white-space: nowrap;
  padding-left: 4px;
  /* Small space before the @ symbol */
  line-height: 36px;
  /* Approximate height of an input field */
}

/* Style for field validation error messages */
.fieldError {
  display: block;
  /* Ensure it takes its own line if needed */
  color: var(--secondary-600);
  /* Klenty red for errors */
  font-size: 12px;
  margin-top: 4px;
  /* Space below the input field */
}

/* New styles for per-domain mailbox sections */
.domainMailboxSection {
  background-color: #fff;
  /* Default background */
  border: 1px solid var(--gray-200);
  /* Klenty-ish subtle border */
  border-radius: 8px;
  /* Klenty-ish border radius */
  /* padding: 24px 32px 32px 32px; /* This padding was from an older version of this class, accordionHeader and accordionContent now handle padding */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  margin-bottom: 24px;
  /* Space between each domain's form section */
  overflow: hidden;
  /* Ensures content respects border-radius */
  /* cursor: pointer; */
  /* Removed: Moved to .accordionHeader */
  /* transition: background-color 0.2s ease; */
  /* Removed: Hover effect moved to .accordionHeader */
}

.domainMailboxSection:last-of-type {
  margin-bottom: 0;
  /* Remove margin from the last section if using a global action bar below */
}

.globalFormActions {
  /* For the main submit button area below all domain sections */
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid var(--gray-200);
  /* justify-content is already flex-end from .formActions */
}

/* Accordion Styles */
.domainMailboxSection {
  border: 1px solid #e2e8f0;
  /* Klenty-ish subtle border */
  border-radius: 6px;
  /* Klenty-ish border radius */
  margin-bottom: 20px;
  background-color: #fff;
  overflow: hidden;
  /* Ensures content respects border-radius */
}

.accordionHeader {
  padding: 12px 16px;
  /* Adjusted padding slightly */
  cursor: pointer;
  background-color: #fff;
  border-bottom: 1px solid transparent;
  transition: background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* Hover effect for accordion header when its parent section is closed */
.domainMailboxSection:not(.accordionOpen) .accordionHeader:hover {
  background-color: var(--gray-50);
  /* Light hover, e.g., #f8f9fa */
}

/* New container for title and (X/Y) indicator */
.accordionTitleContainer {
  display: flex;
  /* flex-direction: column; */
  /* Changed from column */
  flex-direction: row;
  /* To make items side-by-side */
  align-items: baseline;
  /* Align text along their baseline */
  gap: 8px;
  /* Adjusted gap for horizontal spacing */
}

.accordionTitleGroup {
  display: flex;
  align-items: baseline;
  gap: 5px;
}

.accordionHeader h3 {
  margin: 0;
  /* Reset margin */
  font-size: 16px;
  font-weight: 500;
  color: #2d3748;
}

.accordionIcon {
  display: flex;
  /* For centering SVG if needed */
  align-items: center;
  justify-content: center;
  color: #4a5568;
  /* Icon color */
  transition: transform 0.2s ease-in-out;
  /* Smooth rotation */
  transform: rotate(0deg);
  /* Default state (pointing down, for open accordion) */
  flex-shrink: 0;
  /* Prevent shrinking */
}

.accordionIcon svg {
  width: 10px;
  /* Control size via CSS */
  height: 6px;
  display: block;
  /* Remove extra space below SVG */
}

/* When accordion is closed, icon should point right. Default SVG is down, so rotate -90deg */
/* This rule applies when iconOpen class is NOT present */
.accordionIcon:not(.iconOpen) {
  transform: rotate(-90deg);
}

/* If .iconOpen is present, it means accordion is open, icon should point down (default SVG state, 0deg rotation) */
.accordionIcon.iconOpen {
  transform: rotate(0deg);
}

.accordionDomainName {
  margin: 0;
  /* Reset margin */
  font-size: 16px;
  /* Matching h3 */
  font-weight: 500;
  /* Matching h3 */
  color: var(--primary-600);
}

.accordionContent {
  padding: 16px;
  /* border-top: 1px solid #e2e8f0; /* Optional: if header border-bottom is removed when open */
}

.mailboxEntryRow {
  display: flex;
  /* align-items: center; */
  /* Changed from center */
  align-items: flex-start;
  /* Align items to the top */
  gap: 12px;
}

.mailboxRowNumber {
  color: var(--gray-500);
  font-size: 14px;
  font-weight: 500;
  width: 20px;
  /* Fixed width for alignment */
  text-align: right;
  /* Align numbers to the right for a neat column */
  flex-shrink: 0;
  /* Prevent shrinking */
}

.formInputGroup {
  /* Wrapper for each input in the row */
  flex: 1;
  /* Allow input groups to share space */
  min-width: 0;
  /* Prevent overflow issues in flex items */
}

/* .formInput class is already defined and should be sufficient for the inputs themselves */

.mailboxDomainSuffix {
  color: var(--gray-700);
  font-size: 14px;
  white-space: nowrap;
  padding-left: 4px;
  /* Small space before the @ symbol */
}

/* Styles for the per-domain action footer */
.domainActionFooter {
  display: flex;
  justify-content: flex-end;
  /* Aligns button to the right */
  padding: 16px;
  /* Space above the button */
  /* Additional space if needed, or can be combined with padding-top */
  /* Line separator above the button */
}

.domainMailboxSection:last-of-type {
  margin-bottom: 0;
  /* Remove margin from the last section if using a global action bar below */
}

.createMailboxesContainer {
  display: flex;
  flex-direction: column;
  border: 1px solid var(--gray-200);
  border-radius: 6px;
  padding: 24px;
}

.createMailboxesFooter {
  display: flex;
  justify-content: flex-end;
}

/* Mailbox Details Table Styles */
.mailboxDetailsTableContainer {
  /* margin-top: 20px; */
  /* Removed, container provides its own context */
  background-color: var(--gray-50);
  /* Very light gray background for the container card */
  border-radius: 8px;
  padding: 20px 0;
  /* Padding around the table within the card */
  /* overflow-x: auto; /* Consider re-adding if table content can be very wide */
}

.mailboxDetailsTable {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  /* Base font size for table content */
  background-color: #fff;
  /* Table background is white */
}

.mailboxDetailsTable th,
/* General selector for TH and TD */
.mailboxDetailsTable td {
  text-align: left;
  padding: 14px 16px;
  /* Consistent padding, adjust as needed */
  border-bottom: 1px solid var(--gray-200);
  /* Default bottom border for all cells */
}

.mailboxDetailsTable th {
  background-color: #fff;
  /* Header background white */
  font-weight: 500;
  /* Medium weight for header text */
  color: var(--gray-600);
  /* Lighter color for header text */
  font-size: 13px;
  border-top: 1px solid var(--gray-200);
  /* Top border for header cells */
  /* border-bottom is already applied from the general th, td rule */
}

/* Remove bottom border from the last row of data cells */
.mailboxDetailsTable tbody tr:last-child td {
  border-bottom: none;
}

.actionsCell {
  text-align: right;
  /* Align icons to the right */
  white-space: nowrap;
}

.actionIcon {
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px;
  margin: 0 0 0 8px;
  /* Margin to the left of each icon for spacing, if icons are side-by-side */
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.actionIcon:first-child {
  margin-left: 0;
  /* No left margin for the first icon in the cell */
}

.actionIcon svg {
  width: 16px;
  /* Default size, can be overridden */
  height: 16px;
  /* Default size, can be overridden */
  vertical-align: middle;
}

/* Specific hover effects for edit/delete SVGs (assuming they have a default stroke) */
.editIcon:hover svg {
  stroke: #007bff;
  /* Blue color for edit */
}

.deleteIcon:hover svg {
  stroke: #dc3545;
  /* Red color for delete */
}

/* If you want the whole button to have a background on hover */
/* .actionIcon:hover {
  background-color: #f0f0f0;
  border-radius: 4px;
} */

.createdCountIndicator {
  background-color: var(--green-100);
  /* Existing light blueish purple */
  color: var(--green-800);
  /* Existing darker purple text */
  padding: 5px 12px;
  /* Slightly more padding */
  border-radius: 16px;
  /* More pill-like */
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  flex-shrink: 0;
}

.accordionActions {
  display: flex;
  align-items: center;
  gap: 12px;
  /* Space between Add More button and toggle icon */
  flex-wrap: nowrap;
  /* Prevent wrapping */
}

/* Styles for Auto-Generate Tab in CreateMailboxesPage */
.autoGenerateSection {
  padding-top: 16px;
  /* Add some spacing from the tabs */
}

.autoGenNameInputsRow {
  display: flex;
  gap: 20px;
  /* Spacing between First Name and Last Name input groups */
  margin-bottom: 24px;
}

/* Generic Form Label - adjust if a suitable one already exists or merge styles */
.formLabel {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: var(--gray-700);
}

.usernameLabelContainer {
  margin-bottom: 8px;
}

.usernameSuggestionArea {
  background-color: var(--gray-50);
  /* Adjusted: Very light purplish-gray, e.g. #F8F9FE or a Klenty var */
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  /* Ensure text within placeholder is centered */
  min-height: 160px;
  display: flex;
  flex-direction: column;
  align-items: center;
  /* Center placeholder content horizontally */
  justify-content: center;
  /* Center placeholder content vertically */
  /* border: 1px solid var(--gray-200); */
  /* Removed border */
}

/* Override centering if suggestions are present and area should be filled */
.usernameSuggestionArea.hasSuggestions {
  align-items: stretch;
  justify-content: flex-start;
  text-align: left;
  /* Text align for suggestions grid */
  /* padding: 20px; */
  /* Padding is consistent, no need to repeat if same */
}

.suggestionAreaTitle {
  font-size: 16px;
  font-weight: 600;
  color: var(--gray-800);
  /* Standard dark text */
  margin: 0 0 10px 0;
}

.suggestionAreaDescription {
  font-size: 14px;
  color: var(--gray-600);
  /* Lighter gray text */
  line-height: 1.5;
  max-width: 320px;
  margin: 0 auto;
}

/* Styles for Auto-Generated Username Suggestions */
.suggestionsGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  /* 2 columns */
  gap: 16px 24px;
  /* row-gap column-gap - adjusted gap */
  /* margin-bottom: 20px; /* Removed, parent .usernameSuggestionArea.hasSuggestions handles padding */
  width: 100%;
  /* Ensure grid takes full width of its container */
}

.suggestionItem {
  display: flex;
  align-items: center;
  /* Vertically center checkbox, input, suffix */
  /* background-color: #fff; */
  /* Will be handled by input style */
  /* border: 1px solid var(--gray-200); */
  /* Will be handled by input style */
  /* border-radius: 6px; */
  /* padding: 10px 12px; */
  /* Padding will be on input/elements */
  /* transition: border-color 0.2s ease, box-shadow 0.2s ease; */
  gap: 8px;
  /* Gap between checkbox, input, and suffix */
}

/* .suggestionItem:hover {
  border-color: var(--primary-300);
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
} */
/* Hover effect might be better on the input itself */

.suggestionCheckbox {
  margin-right: 0;
  /* Gap is handled by suggestionItem */
  width: 16px;
  height: 16px;
  accent-color: var(--primary-600);
  cursor: pointer;
  flex-shrink: 0;
  border-radius: 6px;
}

.suggestionUsernameInput {
  flex-grow: 1;
  /* Take available space */
  padding: 8px 12px;
  font-size: 14px;
  color: var(--gray-800);
  background-color: #fff;
  border: 1px solid var(--gray-300);
  border-radius: 6px;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  min-width: 0;
  /* Prevent overflow in flex container */
}

.suggestionUsernameInput:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 2px var(--primary-100);
}

.suggestionDomainSuffix {
  font-size: 14px;
  color: var(--gray-700);
  /* Darker as per image */
  margin-left: 0;
  /* Gap is handled by suggestionItem */
  white-space: nowrap;
  flex-shrink: 0;
}

/* Footer for the action button within the auto-gen area */
.autoGenActionFooter {
  display: flex;
  justify-content: flex-end;
  /* Aligns button to the right */
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid var(--gray-200);
  /* Separator line */
}

/* Step Indicator Styles */
.stepIndicatorContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
}

.stepItem {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stepCircle {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--gray-100);
  /* Upcoming step */
  color: var(--gray-500);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 500;
  border: 1px solid var(--gray-300);
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

.stepCircleActive {
  background-color: var(--primary-500);
  /* Active step */
  color: white;
  border-color: var(--primary-500);
}

.stepCircleCompleted {
  background-color: var(--primary-500);
  /* Completed step */
  color: white;
  border-color: var(--primary-500);
}

.stepLabel {
  margin-top: 8px;
  font-size: 14px;
  color: var(--gray-500);
  /* Upcoming label */
  font-weight: 500;
  text-align: center;
}

.stepLabelActive {
  color: var(--primary-500);
  /* Active label */
  font-weight: 600;
}

.stepLabelCompleted {
  color: var(--primary-500);
  /* Completed label - can be same as active or a bit more subdued */
  font-weight: 600;
}

.stepsLineBase {
  background-color: var(--gray-300);
  /* Base line color for upcoming parts */
  margin: 0 10px;
  max-width: 100px;
  /* Adjust as needed */
  position: relative;
  top: -16px;
  /* Corrected for 32px height circles */
  height: 2px;
  /* Ensure height is explicitly defined */
  flex-grow: 1;
  /* Ensure it still tries to grow */
}

.stepsLineProgress {
  height: 100%;
  width: 0%;
  /* Initially no progress */
  background-color: var(--primary-500);
  /* Progress line color */
  transition: width 0.3s ease;
}

/* Progress states for lines */
/* First line (Domain -> Mailboxes) */
.progressState0 .stepsLineProgress {
  width: 0%;
}

.progressState1 .stepsLineProgress {
  width: 100%;
}

/* Line between 1 and 2 is full */

/* Second line (Mailboxes -> Complete) */
/* This would typically be controlled by a different class or state for the second line */
/* For DomainSearch.tsx, only the first line's state might change if we were to show progress to step 2 within this component */
/* Example: if step 2 (Mailboxes) becomes active, the first line would be .progressState1 */

/* --- Styles for SetupCompletePage.tsx --- */

/* Reinstated and adjusted styles for the main page container */

/* New card for the main content below step indicator */
.setupContentCard {
  background-color: #fff;
  border: 1px solid var(--gray-200);
  border-radius: 12px;
  padding: 40px;
  /* Generous internal padding */
  margin-top: 24px;
  /* Space below step indicator */
  margin-bottom: 40px;
  /* Space at the bottom of the page */
  width: 100%;
  max-width: 1100px;
  /* Max width for this content card */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  align-items: center;
  /* Center content like button if it's not full width */
}

.successSection {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin-bottom: 32px;
  /* Space below success message, before the grid */
}

.successCheckmarkIcon {
  margin-bottom: 20px;
  /* Space between large check and title */
  /* SVG specific styles like size are in the SVG component itself for now */
}

.successTitle {
  font-size: 28px;
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: 8px;
}

.successSubtitle {
  font-size: 16px;
  color: var(--gray-600);
  max-width: 480px;
  line-height: 1.6;
}

.mailboxesGrid {
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  width: 100%;
  flex-wrap: wrap;
}

.domainCard {
  min-width: 427px;
  background-color: #fff;
  border: 1px solid var(--gray-200);
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
}

.domainCardHeader {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--gray-100);
}

.globeIcon {
  color: var(--primary-500);
  /* Klenty purple for the globe icon */
  margin-right: 10px;
  flex-shrink: 0;
}

.domainName {
  font-size: 16px;
  font-weight: 600;
  color: var(--gray-800);
  flex-grow: 1;
}

.mailboxCreatedBadge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  background-color: var(--green-100);
  /* Light green background */
  color: var(--green-800);
  /* Darker green text */
  font-size: 13px;
  font-weight: 500;
  padding: 6px 10px;
  border-radius: 16px;
  /* Pill shape */
  flex-shrink: 0;
}

.badgeCheckmarkIcon {
  /* SVG color set via stroke in component, size via width/height in component */
  flex-shrink: 0;
}

.mailboxList {
  display: flex;
  flex-direction: column;
  gap: 12px;
  /* Space between mailbox items */
}

.mailboxItem {
  display: flex;
  align-items: center;
  background-color: var(--primary-50);
  /* Very light purple background */
  padding: 12px 16px;
  border-radius: 8px;
}

.envelopeIcon {
  color: var(--primary-500);
  /* Klenty purple for envelope */
  margin-right: 12px;
  flex-shrink: 0;
}

.mailboxInfo {
  display: flex;
  flex-direction: column;
}

.mailboxFullName {
  font-size: 14px;
  font-weight: 500;
  color: var(--gray-800);
  margin-bottom: 2px;
}

.mailboxEmail {
  font-size: 13px;
  color: var(--gray-600);
}

.addMoreSection {
  margin-top: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.addMoreButton {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  font-size: 15px;
  font-weight: 500;
  color: var(--primary-50);
  background-color: var(--primary-500);
  border-radius: 8px;
  border: 1px solid var(--primary-500);
  cursor: pointer;
  transition: all 0.2s ease;
}

.addMoreButton .arrowIcon {
  /* For specific arrow styling if needed */
  color: var(--primary-50);
}



/* Basic styling for the fallback button in SetupCompletePage */
.styledButton {
  padding: 10px 16px;
  background-color: var(--primary-600);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  margin-top: 16px;
}

.styledButton:hover {
  background-color: var(--primary-700);
}

/* New Styles for PurchaseProgressModal from style.css */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(55, 65, 81, 0.75);
  /* var(--gray-700) with opacity */
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
}

.modalContainer {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-width: 32rem;
  /* 512px */
  width: 100%;
  overflow: hidden;
  margin: 0 16px;
  /* For small screen spacing */
}

.modalPurchaseProgress {
  /* Specific adjustments for this modal */
  max-width: 420px;
  /* Narrower to match design */
}

.modalPurchaseProgress .modalHeader {
  /* Targeting .modalHeader inside .modalPurchaseProgress */
  text-align: center;
  padding-top: 24px;
  padding-bottom: 12px;
  border-bottom: none;
  background-color: var(--gray-50);
  /* Match other modal headers if any, or keep white */
}

.modalPurchaseProgress .modalHeader h3 {
  font-size: 22px;
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: 8px;
}

.modalPurchaseProgress .modalSubtitle {
  font-size: 15px;
  color: var(--gray-600);
  margin-top: 0;
  margin-bottom: 24px;
}

.modalPurchaseProgress .modalContent {
  padding: 16px 32px 32px 32px;
}

.loaderContainer {
  margin-bottom: 28px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.customLoader {
  width: 56px;
  height: 56px;
  border: 6px solid var(--primary-200);
  /* Lighter purple base */
  border-top-color: var(--primary-500);
  /* Darker purple for spinner part */
  border-radius: 50%;
  animation: rotation 0.8s linear infinite;
}

@keyframes rotation {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.progressStepsAlt {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.progressStepAlt {
  display: flex;
  align-items: center;
  gap: 12px;
}

.progressStepIconAlt {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.statusCompleted .progressStepIconAlt {
  /* Combined class for specificity */
  background-color: var(--green-100);
}

.statusCompleted .progressStepIconAlt::before {
  content: '\2713';
  /* Unicode checkmark */
  color: var(--green-600);
  font-size: 14px;
  font-weight: bold;
}

.statusInProgress .progressStepIconAlt {
  background-color: var(--primary-500);
  width: 12px;
  height: 12px;
  margin: 6px;
  /* Centering the smaller dot */
}

.statusPending .progressStepIconAlt {
  background-color: var(--gray-200);
}

.statusPending .progressStepIconAlt::before {
  content: '';
  display: block;
  width: 8px;
  height: 8px;
  background-color: var(--gray-400);
  border-radius: 50%;
}

.progressStepLabelAlt {
  font-size: 15px;
  color: var(--gray-700);
}

.statusCompleted .progressStepLabelAlt {
  /* Combined class for specificity */
  color: var(--gray-500);
}

.statusInProgress .progressStepLabelAlt {
  /* Combined class for specificity */
  color: var(--primary-600);
  font-weight: 500;
}

/* General Page Styles */
.pageContainer {
  padding: 24px;
  background-color: #f9fafb;
  /* Assuming a light gray background */
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.titleGroup h1 {
  font-size: 28px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 4px 0;
}

.titleGroup p {
  font-size: 14px;
  color: #4b5563;
  margin: 0;
}

.purchaseButton {
  background-color: #6366f1;
  /* Klenty Purple/Indigo */
  color: white;
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.purchaseButton svg {
  margin-right: 8px;
}

/* Tabs Styling */
.tabNav {
  display: flex;
  margin-bottom: 24px;
}

/* Domain Card Styling */
.domainCard {
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 24px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.05), 0 1px 2px 0 rgba(0, 0, 0, 0.03);
}

.domainHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
}

.domainNameGroup {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.domainNameGroup svg {
  margin-right: 10px;
  color: #6b7280;
  /* Icon color */
}

.domainMenuButton {
  background-color: transparent;
  border: none;
  cursor: pointer;
  color: #6b7280;
}

/* Mailbox Table Styling */
.mailboxTable {
  width: 100%;
  border-collapse: collapse;
}

.mailboxTable th,
.mailboxTable td {
  padding: 12px 20px;
  text-align: left;
  font-size: 14px;
  border-bottom: 1px solid #f3f4f6;
  /* Lighter border for rows */
}

.mailboxTable th {
  background-color: #f9fafb;
  /* Light gray header for table */
  color: #374151;
  font-weight: 500;
}

.mailboxTable td {
  color: #4b5563;
}

.mailboxTable tr:last-child td {
  border-bottom: none;
}

.statusLink {
  color: #3b82f6;
  /* Blue link color */
  text-decoration: none;
}

.statusLink:hover {
  text-decoration: underline;
}

.actionCell button {
  background: none;
  border: none;
  cursor: pointer;
  margin-right: 12px;
  color: #6b7280;
}

.actionCell button:hover {
  color: #374151;
}

.actionCell button:last-child {
  margin-right: 0;
}

.actionCell svg {
  width: 16px;
  height: 16px;
}

/* Placeholder for Reports Content */
.reportsContent {
  padding: 20px;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.userDomainsPageContainer {
  display: flex;
  /* gap: 24px; /* Gap is now handled by mainContentArea padding */
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
  background-color: var(--gray-50);
  /* Light background for the whole page */
}

.verticalNavContainer {
  display: block;
  text-align: left;
  width: 100%;
  cursor: pointer;
  gap: 16px;
  margin-right: 24px;
  border-bottom: 1px solid var(--gray-200);
  padding: 0;
}

.navTabButton {
  display: block;
  width: 100%;
  text-align: left;
  padding: 12px 16px;
  border: none;
  background-color: transparent;
  font-size: 14px;
  font-weight: 500;
  color: var(--gray-700);
  cursor: pointer;
}

.navTabButtonActive {
  background-color: var(--primary-50);
  color: var(--primary-500);
}

.statusBadge {
  display: inline-flex;
  padding: 4px 10px;
  border-radius: 10px;
  font-size: 12px;
  font-weight: 1000;
  text-align: center;
}

.activeStatusBadge {
  background-color: var(--green-100);
  color: var(--green-600);
}

.pendingStatusBadge {
  background-color: var(--amber-100);
  color: var(--amber-500);
}

.verticalNav {
  width: 240px;
  /* Sidebar width */
  background-color: #ffffff;
  /* White background for sidebar */
  padding: 20px 0px;
  /* Vertical padding, no horizontal as buttons will take full width */
  border-right: 1px solid var(--gray-200);
  flex-shrink: 0;
  height: 100vh;
  /* Make sidebar full viewport height */
  position: sticky;
  /* Keep sidebar visible on scroll */
  top: 0;
}

.navTabButton {
  display: block;
  width: calc(100% - 32px);
  /* Full width minus horizontal padding */
  text-align: left;
  padding: 12px 16px;
  margin: 0 16px 8px 16px;
  /* Side margins for padding, bottom margin for spacing */
  border: none;
  background-color: transparent;
  font-size: 14px;
  font-weight: 500;
  color: var(--gray-700);
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s ease, color 0.2s ease;
}

.navTabButton:hover {
  background-color: var(--gray-100);
  color: var(--gray-900);
}

.navTabButtonActive {
  background-color: var(--primary-50);
  color: var(--primary-600);
  font-weight: 600;
}

.navTabButtonActive:hover {
  background-color: var(--primary-100);
  /* Slightly different hover for active */
  color: var(--primary-700);
}

.mainContentArea {
  flex-grow: 1;
  padding: 24px 32px;
  /* Padding for the content area */
  overflow-y: auto;
  /* Allow content to scroll independently */
}

/* Styles for the Your Domains table view */
.domainListFiltersContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  /* padding: 16px; */
  /* Removed padding */
  /* background-color: #ffffff; */
  /* Removed white background */
  /* border-radius: 8px; */
  /* Removed border-radius */
  /* border: 1px solid var(--gray-200); */
  /* Removed border */
}

.domainFilterDropdownLabel {
  /* This class was for the old static label, will be replaced by .domainFilterDropdown */
  font-size: 14px;
  font-weight: 500;
  color: var(--gray-700);
  padding: 8px 12px;
  border: 1px solid var(--gray-300);
  border-radius: 6px;
  background-color: var(--gray-50);
  /* Light background for the label part */
}

/* New styles for the select dropdown to match image */
.domainFilterDropdown {
  padding: 10px 12px;
  font-size: 14px;
  border: 1px solid var(--gray-300);
  border-radius: 6px;
  background-color: white;
  color: var(--gray-700);
  min-width: 180px;
  /* Adjust as needed, e.g., to fit "Domain: exploreearth.com" */

  /* Hide default arrow */
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;

  /* Custom arrow */
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='%236B7280'%3E%3Cpath fill-rule='evenodd' d='M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z' clip-rule='evenodd' /%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 1em;
  /* Adjust size as needed */

  padding-right: 35px;
  /* Adjust to make space for the arrow: 12px (original right padding) + arrow width + some space */
}

.searchInputWrapperAlt {
  position: relative;
  display: flex;
  align-items: center;
  width: 280px;
  /* Adjusted width to better match image proportion */
}

.searchIconAlt {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray-400);
  pointer-events: none;
  /* So it doesn't interfere with input click */
}

.searchInputAlt {
  width: 100%;
  padding: 10px 12px 10px 36px;
  /* Left padding for icon */
  font-size: 14px;
  border: 1px solid var(--gray-300);
  border-radius: 6px;
  transition: all 0.2s;
}

.searchInputAlt:focus {
  outline: none;
  border-color: var(--primary-400);
  box-shadow: 0 0 0 2px var(--primary-100);
}

/* .mailboxDetailsTableContainer, .mailboxDetailsTable, .tableTh, .tableTd, .actionsCell, .iconButton, .statusBadge, .activeStatusBadge, .pendingStatusBadge, .noResultsText are assumed to be styled correctly from AllMailboxesView or are general enough */

.emptyStateContainerFullWidth {
  text-align: center;
  padding: 40px;
  margin-top: 24px;
  background-color: #ffffff;
  border: 1px solid var(--gray-200);
  border-radius: 8px;
}

.emptyStateContainerFullWidth p {
  font-size: 16px;
  color: var(--gray-600);
  margin-bottom: 16px;
}

/* .primaryButton is assumed to be styled correctly */

.placeholderViewContainer {
  padding: 20px;
  /* background-color: #ffffff; */
  /* Removed as pageHeader now exists inside */
  /* border: 1px solid var(--gray-200); */
  /* Removed */
  /* border-radius: 8px; */
  /* Removed */
  text-align: left;
  /* Changed from center */
}

.placeholderViewContainer .pageHeader {
  margin-bottom: 24px;
}

.placeholderViewContainer p {
  font-size: 16px;
  color: var(--gray-700);
  padding: 24px;
  background-color: #ffffff;
  border: 1px solid var(--gray-200);
  border-radius: 8px;
  text-align: center;
}

/* Ensure .pageHeader, .pageTitle, .pageSubtitle, .primaryButton are styled generally or adapt if needed */

/* Re-check .simpleTabContainer and .tabActive if they were used for UserDomainsPage, 
   they are now replaced by .verticalNav, .navTabButton, .navTabButtonActive */

/* Ensure table styles are robust. From AllMailboxesView, these should be good: */
/* .mailboxDetailsTableContainer - add if specific background/padding for the table card is needed */
/* .mailboxDetailsTable - main table styles */
/* .tableTh, .tableTd - header and data cell styles */
/* .actionsCell - for action buttons alignment */
/* .iconButton - for styling edit/delete buttons */
/* .statusBadge, .activeStatusBadge, .pendingStatusBadge - for status display */

/* Styles for the text when no results are found in a table */
.noResultsText {
  padding: 20px;
  text-align: center;
  color: var(--gray-600);
  font-size: 14px;
}

/* Ensure .userNameSubtext (if used in Domain details under domainName) is styled */
.userNameSubtext {
  font-size: 0.8em;
  color: var(--gray-500);
  margin-top: 2px;
}

.domainsPageHeader {
  position: relative;
  display: flex;
  /* Added */
  justify-content: space-between;
  /* Added */
  align-items: center;
  /* Added */
}


/* Styles from CreateMailboxModal.module.css */

.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--modal-overlay-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  animation: fadeIn 0.2s forwards;
}

.modalContainer {
  background: var(--modal-bg);
  border-radius: var(--modal-border-radius);
  box-shadow: var(--modal-box-shadow);
  width: 100%;
  max-width: 520px;
  /* Matches design */
  margin: 16px;
  display: flex;
  flex-direction: column;
  transform: scale(0.95);
  opacity: 0;
  animation: fadeInScaleUp 0.25s 0.1s forwards;
}

/* @keyframes fadeIn is already defined above if used by domainDropdown */
/* Ensuring it's present if not already defined */
@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

@keyframes fadeInScaleUp {
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--modal-header-padding);
  border-bottom: 1px solid #E5E7EB;
  /* gray-200 */
}

.modalTitle {
  font-size: 20px;
  /* As per image */
  font-weight: 600;
  color: var(--modal-title-color);
  margin: 0;
}

.closeButton {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  color: var(--close-button-color);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.closeButton:hover {
  color: var(--close-button-hover-color);
  background-color: #F3F4F6;
  /* gray-100 */
}

.modalBody {
  padding: var(--modal-body-padding);
}

.modalSubtitle {
  font-size: 14px;
  /* As per image */
  color: var(--modal-subtitle-color);
  margin-top: 0;
  margin-bottom: 20px;
  /* Space before dropdown area */
  line-height: 1.5;
}

.modalFooter {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: var(--modal-footer-padding);
  border-top: 1px solid #E5E7EB;
  /* gray-200 */
  gap: 12px;
}

.cancelButton,
.submitButton {
  /* .submitButton styles might conflict if a global .submitButton exists, but modal one uses specific vars */
  padding: 10px 18px;
  font-size: 14px;
  font-weight: 500;
  border-radius: var(--button-border-radius);
  border: 1px solid transparent;
  cursor: pointer;
  transition: background-color 0.2s ease, border-color 0.2s ease;
}

.cancelButton {
  background-color: #fff;
  /* White background */
  color: #374151;
  /* gray-700 text */
  border: 1px solid #D1D5DB;
  /* gray-300 border */
}

.cancelButton:hover {
  background-color: #F9FAFB;
  /* gray-50 for hover */
}

.submitButton {
  /* This is specific to the modal, using its vars */
  background-color: var(--klenty-purple);
  color: white;
  border-color: var(--klenty-purple);
}

.submitButton:hover {
  background-color: #4F46E5;
  /* Darker Klenty purple. Could use var(--button-primary-hover-bg) if it matches */
  border-color: #4F46E5;
}

/* Styles for Custom Multi-Select Dropdown */
.customDropdown {
  position: relative;
  margin-bottom: 20px;
  /* Or adjust as needed */
}

.dropdownInputArea {
  display: flex;
  flex-wrap: wrap;
  /* Allow pills to wrap */
  align-items: center;
  border: 1px solid var(--klenty-purple);
  /* Purple border as per image */
  border-radius: var(--button-border-radius);
  padding: 6px 12px;
  /* Adjust padding for pills and arrow */
  min-height: 40px;
  /* Approximate input height */
  cursor: pointer;
  gap: 6px;
  /* Gap between pills */
  position: relative;
  /* For dropdownArrow positioning */
}

.dropdownInputArea:focus-within {
  box-shadow: 0 0 0 2px var(--klenty-light-purple);
}

.dropdownPlaceholderText {
  color: #9CA3AF;
  /* gray-400 */
  font-size: 14px;
  line-height: 28px;
  /* Align with pill height roughly */
  padding-left: 2px;
  /* Small offset */
}

.domainPill {
  display: inline-flex;
  align-items: center;
  background-color: #F3F4F6;
  /* gray-100 */
  color: #374151;
  /* gray-700 */
  border-radius: 4px;
  /* Slightly less rounded than main border */
  padding: 4px 8px;
  font-size: 13px;
  font-weight: 500;
  gap: 6px;
  line-height: 1.2;
}

.pillRemoveIcon {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  margin-left: 2px;
  /* Small space */
  color: #6B7280;
  /* gray-500 */
  display: flex;
  align-items: center;
}

.pillRemoveIcon:hover {
  color: #1F2937;
  /* gray-800 */
}

.dropdownArrow {
  margin-left: auto;
  /* Pushes arrow to the right */
  display: flex;
  align-items: center;
  color: var(--klenty-purple);
  /* Purple arrow to match border */
  padding-left: 8px;
  /* Space from pills/placeholder */
}

.dropdownOptionsList {
  position: absolute;
  top: calc(100% + 4px);
  /* Small gap below the input area */
  left: 0;
  right: 0;
  background-color: var(--modal-bg);
  border: 1px solid #E5E7EB;
  /* gray-200 */
  border-top: none;
  /* As input area has bottom border */
  border-radius: 0 0 var(--button-border-radius) var(--button-border-radius);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  max-height: 200px;
  /* Limit height and enable scroll */
  overflow-y: auto;
  z-index: 1010;
  padding: 4px 0;
  /* Small vertical padding for the list */
}

.dropdownOption {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  font-size: 14px;
  color: #374151;
  /* gray-700 */
  cursor: pointer;
  gap: 10px;
  transition: background-color 0.15s ease;
}

.dropdownOption:hover {
  background-color: #F3F4F6;
  /* gray-100 */
}

.dropdownOption.selectedOption {
  /* For the selected option in the list */
  background-color: var(--klenty-light-purple);
  color: var(--klenty-purple);
  font-weight: 500;
}

.optionCheckbox {
  width: 16px;
  height: 16px;
  margin-right: 0;
  /* Gap handled by parent */
  accent-color: var(--klenty-purple);
  cursor: pointer;
  border-radius: 4px;
  /* For visual consistency if browser styles it */
  border: 1px solid var(--klenty-purple);
  /* Ensure border color matches accent */
}

/* The .dropdownPlaceholder class from modal css can be removed if it was only for the dashed box */

.inputWithSuffixContainer {
  display: flex;
  align-items: center;
  gap: 8px;
}

.inputWithSuffixContainer .formInput {
  flex-grow: 1;
}

.inputSuffix {
  font-size: 14px;
  color: var(--gray-600);
  white-space: nowrap;
  padding: 0 4px;
  line-height: 38px;
}

.formLabel {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: var(--gray-700);
}

.availabilityTag {
  display: inline-block;
  padding: 5px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  flex-shrink: 0;
  margin-left: 8px;
}

.available {
  background-color: var(--green-100);
  color: var(--green-800);
  border: 1px solid var(--green-600);
}

.unavailable {
  background-color: var(--red-100);
  color: var(--red-800);
  border: 1px solid var(--red-800);
}

.emptyDomainContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: 48px;
  background: #fff;
}

.emptyDomainContent {
  max-width: 420px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.emptyDomainSubtitle {
  color: #6b7280;
  font-size: 1.05rem;
  margin-bottom: 0px;
  margin-top: 4px;
}

.emptyDomainFeatures {
  margin: 12px 0 18px 0;
}

.emptyDomainUse {
  color: #374151;
  font-weight: 500;
  font-size: 0.98rem;
}

.emptyDomainFeatureItem {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  color: #374151;
  font-size: 0.97rem;
  margin: 6px 0;
}

.checkmark {
  color: #6366f1;
  font-size: 1.1rem;
  margin-top: 2px;
}

.emptyDomainButton {
  margin-top: 24px;
  width: 180px;
  height: 44px;
  font-weight: 700;
  font-size: 1.08rem;
  background: linear-gradient(90deg, #3696E5 0%, #7424DC 100%) !important;
  border: none;
  border-radius: 12px;
  color: #fff;
  box-shadow: 0 4px 16px rgba(55, 65, 81, 0.13);
  transition: background 0.2s, box-shadow 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  letter-spacing: 0.01em;
}

.emptyDomainButton:hover {
  background: linear-gradient(90deg, #2563eb 0%, #7c3aed 100%);
  box-shadow: 0 6px 20px rgba(55, 65, 81, 0.18);
}

.emptyDomainImage {
  display: flex;
  align-items: center;
  justify-content: center;
}

.gradientBox {
  width: 370px;
  height: 250px;
  border-radius: 18px;
  background: radial-gradient(circle at 60% 40%, #a5f3fc 0%, #a78bfa 60%, #f0abfc 100%);
  box-shadow: 0 4px 32px rgba(55, 65, 81, 0.10);
}