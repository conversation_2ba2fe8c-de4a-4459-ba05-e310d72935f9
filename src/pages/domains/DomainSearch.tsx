import React, { use<PERSON><PERSON>back, useEffect, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import { searchDomains } from "../../service/domains";
import styles from "../style.module.css";
import DomainCart from "./DomainCart";
import { Domain } from "./types";

interface DomainSearchProps {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  searchResults: Domain[];
  setSearchResults: (results: Domain[]) => void;
  selectedDomains: Domain[];
  addDomain: (domain: Domain) => void;
  removeDomain: (domainName: string) => void;
  domainCount: number;
  clearResults: () => void;
  setIsModalOpen: (isOpen: boolean) => void;
  onProceedWithSelectedDomains: () => void;
  suggestedDomains: Domain[];
  setSuggestedDomains: (domains: Domain[]) => void;
}

const DomainSearch = ({
  searchTerm,
  setSearchTerm,
  searchResults: contextSearchResults,
  setSearchResults: setContextSearchResults,
  selectedDomains,
  addDomain,
  removeDomain,
  domainCount,
  clearResults,
  setIsModalOpen,
  onProceedWithSelectedDomains,
  suggestedDomains,
  setSuggestedDomains,
}: DomainSearchProps) => {
  const navigate = useNavigate();
  const searchInputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const [isSearching, setIsSearching] = useState(false);
  const [inputValue, setInputValue] = useState("");
  const [showDropdown, setShowDropdown] = useState(false);
  const [noResults, setNoResults] = useState(false);

  const [localSearchResults, setLocalSearchResults] = useState<Domain[]>([]);
  const [dropdownPlaceholderHeight, setDropdownPlaceholderHeight] = useState(0);

  useEffect(() => {
    if (showDropdown && dropdownRef.current) {
      const timerId = setTimeout(() => {
        if (dropdownRef.current) {
          setDropdownPlaceholderHeight(dropdownRef.current.offsetHeight);
        }
      }, 0);
      return () => clearTimeout(timerId);
    } else {
      setDropdownPlaceholderHeight(0);
    }
  }, [
    showDropdown,
    localSearchResults,
    selectedDomains,
    isSearching,
    noResults,
    dropdownRef,
  ]);

  useEffect(() => {
    if (selectedDomains.length === 2 && showDropdown) {
      const timer = setTimeout(() => {
        setShowDropdown(false);
      }, 1500);
      return () => clearTimeout(timer);
    }
  }, [selectedDomains, showDropdown, setShowDropdown]);

  const handleClickOutside = useCallback((event: MouseEvent) => {
    if (
      dropdownRef.current &&
      !dropdownRef.current.contains(event.target as Node) &&
      searchInputRef.current &&
      !searchInputRef.current.contains(event.target as Node)
    ) {
      setShowDropdown(false);
    }
  }, []);

  const handleEscKey = useCallback((event: KeyboardEvent) => {
    if (event.key === "Escape") {
      setShowDropdown(false);
    }
  }, []);

  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    document.addEventListener("keydown", handleEscKey);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      document.removeEventListener("keydown", handleEscKey);
    };
  }, [handleClickOutside, handleEscKey]);

  const fetchDomains = useCallback(async (query: string) => {
    try {
      setIsSearching(true);
      const cleanQuery = query.toLowerCase().trim().replace(/\s+/g, "");

      if (cleanQuery.length < 3) {
        setLocalSearchResults([]);
        setNoResults(false);
        setShowDropdown(false);
        return;
      }
      const results = await searchDomains({ query: cleanQuery });

      setShowDropdown(true);

      if (results.length > 0) {
        setLocalSearchResults(results);
        setNoResults(false);
      } else {
        setLocalSearchResults([]);
        setNoResults(true);
      }
    } catch (error) {
      console.error("Error fetching domains:", error);
      setLocalSearchResults([]);
      setNoResults(true);
      setShowDropdown(true);
    } finally {
      setIsSearching(false);
    }
  }, []);

  useEffect(() => {
    const delayDebounce = setTimeout(() => {
      const trimmedInput = inputValue.trim();

      if (trimmedInput.length >= 3) {
        setIsSearching(true);
        setShowDropdown(true);
        fetchDomains(trimmedInput);
      } else if (trimmedInput.length === 0) {
        setLocalSearchResults([]);
        setNoResults(false);
        setShowDropdown(false);
        if (typeof clearResults === "function") {
          clearResults();
        }
      } else if (trimmedInput.length < 3 && trimmedInput.length > 0) {
        setLocalSearchResults([]);
        setNoResults(false);
        setShowDropdown(false);
      }
    }, 300);

    return () => clearTimeout(delayDebounce);
  }, [inputValue, fetchDomains, clearResults]);

  const handleSearch = useCallback(
    (e?: React.FormEvent) => {
      if (e) {
        e.preventDefault();
      }

      if (inputValue.trim().length < 3) {
        return;
      }

      const cleanDomainName = inputValue
        .toLowerCase()
        .trim()
        .replace(/\s+/g, "");

      setSearchTerm(cleanDomainName);

      setIsSearching(true);
      setShowDropdown(true);

      fetchDomains(cleanDomainName);
    },
    [inputValue, setSearchTerm, fetchDomains]
  );

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputValue(value);

    if (value.trim().length === 0) {
      setShowDropdown(false);
    } else if (value.trim().length >= 3) {
      setShowDropdown(true);
    } else {
      setShowDropdown(false);
    }
  };

  useEffect(() => {
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, []);

  return (
    <div className={styles.domainSearchContainer}>
      <div className={styles.stepIndicatorContainer}>
        <div className={styles.stepItem}>
          <div className={`${styles.stepCircle} ${styles.stepCircleActive}`}>
            1
          </div>
          <div className={`${styles.stepLabel} ${styles.stepLabelActive}`}>
            Domain
          </div>
        </div>
        <div className={`${styles.stepsLineBase} ${styles.progressState0}`}>
          <div className={styles.stepsLineProgress}></div>
        </div>
        <div className={styles.stepItem}>
          <div className={styles.stepCircle}>2</div>
          <div className={styles.stepLabel}>Mailboxes</div>
        </div>
        <div className={`${styles.stepsLineBase} ${styles.progressState0}`}>
          <div className={styles.stepsLineProgress}></div>
        </div>
        <div className={styles.stepItem}>
          <div className={styles.stepCircle}>3</div>
          <div className={styles.stepLabel}>Complete</div>
        </div>
      </div>

      <div className={styles.mailboxContainer}>
        <div className={styles.mailboxHeader}>
          <h2 className={styles.mailboxHeaderH2}>
            Select your domain for email outreach
          </h2>
        </div>

        <div className={styles.mailboxDescription}>
          Type your domain name below to check availability. You can select up
          to 2 domains for optimal deliverability management.
        </div>

        <div className={styles.infoBanner}>
          <div className={styles.infoBannerContent}>
            <h4>Quick Tips To Get You Started:</h4>
            <ul className={styles.tipList}>
              <li className={styles.tipItem}>
                <svg
                  className={styles.listItemInfoIcon}
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <circle cx="12" cy="12" r="10"></circle>
                  <path d="M12 8v4M12 16h.01"></path>
                </svg>
                <div className={styles.tipContent}>
                  <strong className={styles.tipContentHeading}>
                    2 Domains Maximum
                  </strong>
                  <p className={styles.tipContentText}>
                    You can select up to 2 domains. This helps maintain focused
                    email campaigns and better deliverability monitoring.
                  </p>
                </div>
              </li>
              <li className={styles.tipItem}>
                <svg
                  className={styles.listItemInfoIcon}
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <circle cx="12" cy="12" r="10"></circle>
                  <path d="M12 8v4M12 16h.01"></path>
                </svg>
                <div className={styles.tipContent}>
                  <strong className={styles.tipContentHeading}>
                    Only .com and .co Domains
                  </strong>
                  <p className={styles.tipContentText}>
                    For better deliverability, only .com and .co domains will be
                    listed in search results. These domains have higher
                    deliverability rates.
                  </p>
                </div>
              </li>
              <li className={styles.tipItem}>
                <svg
                  className={styles.listItemInfoIcon}
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <circle cx="12" cy="12" r="10"></circle>
                  <path d="M12 8v4M12 16h.01"></path>
                </svg>
                <div className={styles.tipContent}>
                  <strong className={styles.tipContentHeading}>
                    Multiple Mailboxes
                  </strong>
                  <p className={styles.tipContentText}>
                    You can create the same mailbox across all your selected
                    domains at once with the 'Apply to all domains' option.
                  </p>
                </div>
              </li>
            </ul>
          </div>
        </div>

        <form onSubmit={handleSearch} className={styles.domainSearchForm}>
          <div className={styles.searchContainer}>
            <div className={styles.searchInputWrapper}>
              <div className={styles.searchIcon}>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="18"
                  height="18"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <input
                ref={searchInputRef}
                type="text"
                className={styles.searchInput}
                placeholder="Enter your domain name (e.g., acme.com)"
                value={inputValue}
                onChange={handleInputChange}
                autoComplete="off"
                onFocus={() => {
                  if (localSearchResults.length > 0 || noResults) {
                    setShowDropdown(true);
                  }
                }}
              />
              <button
                type="submit"
                className={styles.searchButton}
                disabled={isSearching || inputValue.trim().length < 3}
              >
                {isSearching ? "Checking..." : "Check Availability"}
              </button>
            </div>

            {showDropdown && (
              <div className={styles.domainDropdown} ref={dropdownRef}>
                {isSearching && (
                  <div className={styles.searchingMessage}>
                    <p>Searching for domains...</p>
                  </div>
                )}

                {showDropdown &&
                  selectedDomains.length < 2 &&
                  selectedDomains.length > 0 && (
                    <div className={styles.selectionLimitHint}>
                      You can select up to {2 - selectedDomains.length} more
                      domain(s). (Max 2 total)
                    </div>
                  )}
                {showDropdown &&
                  selectedDomains.length === 0 &&
                  localSearchResults.length > 0 && (
                    <div className={styles.selectionLimitHint}>
                      Select up to 2 domains.
                    </div>
                  )}
                {showDropdown && selectedDomains.length === 2 && (
                  <div className={styles.selectionLimitReachedHint}>
                    Maximum 2 domains selected.
                  </div>
                )}

                {!isSearching && localSearchResults.length > 0 && (
                  <>
                    <div className={styles.domainDropdownList}>
                      {localSearchResults.map((domain) => (
                        <div
                          key={domain.domainName}
                          className={styles.domainDropdownItemNew}
                        >
                          <div className={styles.domainInfoGroup}>
                            <span className={styles.domainNameTextNew}>
                              {domain.domainName}
                            </span>
                            <span
                              className={
                                `${styles.availabilityTag} ${domain.available ? styles.available : styles.unavailable}`
                              }
                            >
                              {domain.available ? "Available" : "Unavailable"}
                            </span>
                          </div>

                          {domain.available && !selectedDomains.some(
                            (d) => d.domainName === domain.domainName
                          ) &&
                            selectedDomains.length < 2 && (
                              <button
                                className={styles.addToCartBtnNew}
                                onClick={() => {
                                  addDomain(domain);
                                }}
                                type="button"
                              >
                                Add Domains
                              </button>
                            )}
                          {selectedDomains.some(
                            (d) => d.domainName === domain.domainName
                          ) && (
                            <div className={styles.domainAddedStateWrapper}>
                              <span className={styles.addedStatusText}>
                                <span className={styles.addedTickIconWrapper}>
                                  <svg
                                    width="14"
                                    height="14"
                                    viewBox="0 0 16 16"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg"
                                  >
                                    <path
                                      d="M13.3334 4L6.00008 11.3333L2.66675 8"
                                      stroke="currentColor"
                                      strokeWidth="2"
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                    />
                                  </svg>
                                </span>
                                Added
                              </span>
                              <button
                                className={styles.removeDomainLink}
                                onClick={() => removeDomain(domain.domainName)}
                                type="button"
                              >
                                Remove
                              </button>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </>
                )}

                {!isSearching && noResults && (
                  <div className={styles.noDomainsMessage}>
                    <p>No domains found matching your search criteria.</p>
                    <p>Try a different search term or check your spelling.</p>
                  </div>
                )}
              </div>
            )}
          </div>
        </form>

        {showDropdown && (
          <div style={{ height: dropdownPlaceholderHeight + "px" }}></div>
        )}

        {domainCount > 0 && (
          <div className={styles.domainCartCounter}>
            <span>{domainCount}/2 domains selected</span>
          </div>
        )}

        {domainCount > 0 && (
          <DomainCart
            selectedDomains={selectedDomains}
            removeDomain={removeDomain}
            domainCount={domainCount}
          />
        )}

        <div className={styles.domainSearchActions}>
          {(inputValue.length > 0 ||
            (showDropdown && (localSearchResults.length > 0 || noResults))) && (
            <button
              className={styles.clearSearchBtn}
              onClick={() => {
                setInputValue("");
              }}
            >
              Clear Search
            </button>
          )}
          {selectedDomains.length > 0 && (
            <button
              className={styles.continueBtn}
              onClick={onProceedWithSelectedDomains}
            >
              Continue with {selectedDomains.length}{" "}
              {selectedDomains.length === 1 ? "domain" : "domains"}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default DomainSearch;
