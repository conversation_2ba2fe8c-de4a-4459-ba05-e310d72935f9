import { handleErrors } from "@klenty/klenty-ui";
import React, { useEffect, useState } from "react";
import DeleteIcon from "../../assets/icons/DeleteIcon";
import EditIcon from "../../assets/icons/EditIcon";
import {
  CreationMailboxConfig,
  MailBoxConfig,
  setupMailBoxForDomains,
  SetupMailBoxForDomainsParams,
} from "../../service/domains";
import styles from "../style.module.css";
import type { CompletedDomainSetup } from "./SetupCompletePage";
import { Domain } from "./types";
import {
  Button,
  BUTTON_TYPES,
  TextInput,
  Table,
  Tabs,
  Tab,
  Checkbox,
} from "@klenty/klenty-ui";

interface CreateMailboxesPageProps {
  selectedDomainsFromSearch: Domain[];
  onCompleteSetup: (completedConfigs: CompletedDomainSetup[]) => void;
}

const INITIAL_SLOT_COUNT = 5;

const CreateMailboxesPage: React.FC<CreateMailboxesPageProps> = ({
  selectedDomainsFromSearch,
  onCompleteSetup,
}) => {
  const [mailboxes, setMailboxes] = useState<SetupMailBoxForDomainsParams["mailboxes"]>(
    []
  );
  const [openDomainIndex, setOpenDomainIndex] = useState<number>(0);

  const [autoGen, setAutoGen] = useState<
    Record<
      string,
      {
        firstName: string;
        lastName: string;
        suggestions: { id: string; username: string; isChecked: boolean }[];
        show: boolean;
      }
    >
  >({});

  const [activeTab, setActiveTab] = useState<Record<string, "auto" | "manual">>(
    {}
  );

  const [viewMode, setViewMode] = useState<Record<string, "form" | "table">>(
    {}
  );

  useEffect(() => {
    const initialMailboxes = (selectedDomainsFromSearch || []).map(
      (domain) => ({
        domain: domain.domainName,
        config: Array.from({ length: INITIAL_SLOT_COUNT }, () => ({
          username: "",
          firstName: "",
          lastName: "",
          errors: {},
        })),
      })
    );
    setMailboxes(initialMailboxes);
    const autoGenInit: typeof autoGen = {};
    const tabInit: typeof activeTab = {};
    const viewInit: typeof viewMode = {};
    initialMailboxes.forEach((mb, idx) => {
      autoGenInit[mb.domain] = {
        firstName: "",
        lastName: "",
        suggestions: [],
        show: false,
      };
      tabInit[mb.domain] = "auto";
      viewInit[mb.domain] = "form";
    });
    setAutoGen(autoGenInit);
    setActiveTab(tabInit);
    setViewMode(viewInit);
  }, [selectedDomainsFromSearch]);

  const handleDomainHeaderClick = (domainIndex: number) => {
    setOpenDomainIndex((prevIndex) =>
      prevIndex === domainIndex ? -1 : domainIndex
    );
  };

  // Tab change
  const handleTabChange = (domain: string, newTab: "auto" | "manual") => {
    setActiveTab((prev) => ({ ...prev, [domain]: newTab }));
  };

  // Manual input change
  const handleMailboxInputChange = (
    domain: string,
    mailboxIndex: number,
    field: keyof Omit<CreationMailboxConfig, "errors">,
    value: string
  ) => {
    setMailboxes((prev) =>
      prev.map((mb) => {
        if (mb.domain !== domain) return mb;
        const updatedConfig = mb.config.map((mailbox, idx) => {
          if (idx !== mailboxIndex) return mailbox;
          const newErrors = { ...mailbox.errors };
          delete newErrors[field];
          return { ...mailbox, [field]: value, errors: newErrors };
        });
        return { ...mb, config: updatedConfig };
      })
    );
  };

  // Auto-gen input change
  const generateUsernameSuggestions = (firstName: string, lastName: string) => {
    const fn = firstName.toLowerCase().trim();
    const ln = lastName.toLowerCase().trim();
    if (!fn || !ln) return [];
    const suggestions = [
      `${fn}.${ln}`,
      `${fn}${ln}`,
      `${fn}${ln.charAt(0)}`,
      `${fn.charAt(0)}${ln}`,
      `${fn}${ln}123`,
    ];
    const unique = Array.from(
      new Set(suggestions.map((s) => s.replace(/[^a-z0-9.-_]/gi, "")))
    );
    return unique
      .slice(0, 5)
      .map((uname, idx) => ({
        id: `suggestion-${idx}-${Date.now()}`,
        username: uname,
        isChecked: true,
      }));
  };

  const handleAutoGenFormInputChange = (
    domain: string,
    field: "firstName" | "lastName",
    value: string
  ) => {
    setAutoGen((prev) => {
      const prevObj = prev[domain] || {
        firstName: "",
        lastName: "",
        suggestions: [],
        show: false,
      };
      const newFirstName = field === "firstName" ? value : prevObj.firstName;
      const newLastName = field === "lastName" ? value : prevObj.lastName;
      if (newFirstName.trim() && newLastName.trim()) {
        return {
          ...prev,
          [domain]: {
            firstName: newFirstName,
            lastName: newLastName,
            suggestions: generateUsernameSuggestions(newFirstName, newLastName),
            show: true,
          },
        };
      } else {
        return {
          ...prev,
          [domain]: {
            firstName: newFirstName,
            lastName: newLastName,
            suggestions: [],
            show: false,
          },
        };
      }
    });
  };

  // Suggestion check toggle
  const handleSuggestionCheckChange = (
    domain: string,
    suggestionId: string
  ) => {
    setAutoGen((prev) => {
      const prevObj = prev[domain];
      if (!prevObj) return prev;
      return {
        ...prev,
        [domain]: {
          ...prevObj,
          suggestions: prevObj.suggestions.map((s) =>
            s.id === suggestionId ? { ...s, isChecked: !s.isChecked } : s
          ),
        },
      };
    });
  };

  // Suggestion username edit
  const handleSuggestedUsernameChange = (
    domain: string,
    suggestionId: string,
    newUsername: string
  ) => {
    setAutoGen((prev) => {
      const prevObj = prev[domain];
      if (!prevObj) return prev;
      return {
        ...prev,
        [domain]: {
          ...prevObj,
          suggestions: prevObj.suggestions.map((s) =>
            s.id === suggestionId ? { ...s, username: newUsername } : s
          ),
        },
      };
    });
  };

  // Create mailboxes for a domain
  const handleCreateSingleDomainMailboxes = (domain: string) => {
    const mbIndex = mailboxes.findIndex((mb) => mb.domain === domain);
    if (mbIndex === -1) return;
    if (activeTab[domain] === "auto") {
      const auto = autoGen[domain];
      if (auto && auto.show && auto.suggestions.length > 0) {
        const checked = auto.suggestions.filter((s) => s.isChecked);
        if (checked.length === 0) return;
        const newConfigs: CreationMailboxConfig[] = checked.map((s) => ({
          username: s.username,
          firstName: auto.firstName,
          lastName: auto.lastName,
        }));
        setMailboxes((prev) =>
          prev.map((mb, idx) =>
            idx === mbIndex ? { ...mb, config: newConfigs } : mb
          )
        );
        setViewMode((prev) => ({ ...prev, [domain]: "table" }));
        setAutoGen((prev) => ({
          ...prev,
          [domain]: {
            firstName: "",
            lastName: "",
            suggestions: [],
            show: false,
          },
        }));
      }
    } else {
      // Manual
      let hasValidationErrors = false;
      const validated = mailboxes[mbIndex].config.map((mb) => {
        const newErrors: NonNullable<CreationMailboxConfig["errors"]> = {};
        const isFirstNameFilled = mb.firstName.trim() !== "";
        const isLastNameFilled = mb.lastName.trim() !== "";
        const isUsernameFilled = mb.username.trim() !== "";
        const filledCount = [
          isFirstNameFilled,
          isLastNameFilled,
          isUsernameFilled,
        ].filter(Boolean).length;
        if (filledCount > 0 && filledCount < 3) {
          if (!isFirstNameFilled)
            newErrors.firstName = "First name is required.";
          if (!isLastNameFilled) newErrors.lastName = "Last name is required.";
          if (!isUsernameFilled) newErrors.username = "Username is required.";
          hasValidationErrors = true;
        }
        return { ...mb, errors: newErrors };
      });
      if (hasValidationErrors) {
        setMailboxes((prev) =>
          prev.map((mb, idx) =>
            idx === mbIndex ? { ...mb, config: validated } : mb
          )
        );
        return;
      }
      const successfullyFilled = validated.filter(
        (mb) => mb.firstName && mb.lastName && mb.username
      );
      if (successfullyFilled.length > 0) {
        setMailboxes((prev) =>
          prev.map((mb, idx) =>
            idx === mbIndex ? { ...mb, config: successfullyFilled } : mb
          )
        );
        setViewMode((prev) => ({ ...prev, [domain]: "table" }));
      }
    }
  };

  // Edit
  const handleEditDomainMailboxes = (domain: string) => {
    setMailboxes((prev) =>
      prev.map((mb) => {
        if (mb.domain !== domain) return mb;
        const existing = mb.config.map((mb) => ({ ...mb, errors: {} }));
        const newNeeded = Math.max(0, INITIAL_SLOT_COUNT - existing.length);
        const newEmpty = Array.from({ length: newNeeded }, () => ({
          username: "",
          firstName: "",
          lastName: "",
          errors: {},
        }));
        return { ...mb, config: [...existing, ...newEmpty] };
      })
    );
    setViewMode((prev) => ({ ...prev, [domain]: "form" }));
    setActiveTab((prev) => ({ ...prev, [domain]: "manual" }));
  };

  // Delete
  const handleDeleteMailbox = (domain: string, mailboxIdx: number) => {
    setMailboxes((prev) =>
      prev.map((mb) => {
        if (mb.domain !== domain) return mb;
        const updated = mb.config.filter((_, idx) => idx !== mailboxIdx);
        if (updated.length === 0 && viewMode[domain] === "table") {
          // Reset to form
          return {
            ...mb,
            config: Array.from({ length: INITIAL_SLOT_COUNT }, () => ({
              username: "",
              firstName: "",
              lastName: "",
              errors: {},
            })),
          };
        }
        return { ...mb, config: updated };
      })
    );
    setViewMode((prev) => {
      if (mailboxes.find((mb) => mb.domain === domain)?.config.length === 1) {
        return { ...prev, [domain]: "form" };
      }
      return prev;
    });
  };

  // Add more
  const handleAddMoreMailboxes = (domain: string) => {
    setMailboxes((prev) =>
      prev.map((mb) => {
        if (mb.domain !== domain) return mb;
        const existing = mb.config;
        const newNeeded = Math.max(0, INITIAL_SLOT_COUNT - existing.length);
        const newEmpty = Array.from({ length: newNeeded }, () => ({
          username: "",
          firstName: "",
          lastName: "",
          errors: {},
        }));
        return { ...mb, config: [...existing, ...newEmpty] };
      })
    );
    setViewMode((prev) => ({ ...prev, [domain]: "form" }));
    setActiveTab((prev) => ({ ...prev, [domain]: "manual" }));
  };

  // Complete setup
  const handleCompleteSetup = async () => {
    return setupMailBoxForDomains({ mailboxes: mailboxes })
      .catch(handleErrors)
      .then(() => {
        // Prepare completed configs for the complete page
        const completedConfigs: CompletedDomainSetup[] = mailboxes.map(
          (mb) => ({
            domain: { name: mb.domain },
            mailboxes: mb.config.map((cfg) => ({
              firstName: cfg.firstName,
              lastName: cfg.lastName,
              username: cfg.username,
            })),
            initialSlotCount: INITIAL_SLOT_COUNT,
          })
        );
        onCompleteSetup(completedConfigs);
      });
  };

  const isAnyMailboxFilled = (config: CreationMailboxConfig[]) =>
    config.some((mb) => mb.firstName || mb.lastName || mb.username);

  const isMailboxCreatedForDomain = (domain: string) =>
    viewMode[domain] === "table" &&
    (mailboxes.find((mb) => mb.domain === domain)?.config.length || 0) > 0;

  const areAllDomainsMailboxesCreated = () =>
    mailboxes.length > 0 &&
    mailboxes.every((mb) => isMailboxCreatedForDomain(mb.domain));

  const getCreateActionText = (domain: string) => {
    if (
      activeTab[domain] === "auto" &&
      autoGen[domain]?.show &&
      autoGen[domain]?.suggestions.length > 0
    ) {
      const checkedCount = autoGen[domain].suggestions.filter(
        (s) => s.isChecked
      ).length;
      if (checkedCount === 0) return "Create Mailbox";
      return checkedCount === 1 ? "Create Mailbox" : "Create Mailboxes";
    } else {
      const mb = mailboxes.find((mb) => mb.domain === domain);
      const filledCount = mb
        ? mb.config.filter((mb) => mb.firstName || mb.lastName || mb.username)
            .length
        : 0;
      return filledCount === 0 || filledCount === 1
        ? "Create Mailbox"
        : "Create Mailboxes";
    }
  };

  if (mailboxes.length === 0) {
    return (
      <div className={styles.domainSearch}>
        <div className={styles.mailboxHeader}>
          <h2 className={styles.mailboxHeaderH2}>Create Mailboxes</h2>
        </div>
        <p className={styles.mailboxDescription}>
          No domains selected for mailbox creation. Please go back and select
          domains first.
        </p>
      </div>
    );
  }

  return (
    <div className={styles.createMailboxesPageContainer}>
      <div className={styles.stepIndicatorContainer}>
        {/* Step 1: Domain - Completed */}
        <div className={styles.stepItem}>
          <div className={`${styles.stepCircle} ${styles.stepCircleCompleted}`}>
            <svg
              width="12"
              height="9"
              viewBox="0 0 12 9"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M10.7539 1.41699L4.43222 7.73866L1.24609 4.55254"
                stroke="white"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </div>
          <div className={`${styles.stepLabel} ${styles.stepLabelCompleted}`}>
            Domain
          </div>
        </div>

        <div className={`${styles.stepsLineBase} ${styles.progressState1}`}>
          {" "}
          {/* progressState1 means line is filled */}
          <div className={styles.stepsLineProgress}></div>
        </div>

        {/* Step 2: Mailboxes - Active */}
        <div className={styles.stepItem}>
          <div className={`${styles.stepCircle} ${styles.stepCircleActive}`}>
            2
          </div>
          <div className={`${styles.stepLabel} ${styles.stepLabelActive}`}>
            Mailboxes
          </div>
        </div>

        <div className={`${styles.stepsLineBase} ${styles.progressState0}`}>
          {" "}
          {/* progressState0 means line is not filled */}
          <div className={styles.stepsLineProgress}></div>
        </div>

        {/* Step 3: Complete - Upcoming */}
        <div className={styles.stepItem}>
          <div className={styles.stepCircle}>3</div>
          <div className={styles.stepLabel}>Complete</div>
        </div>
      </div>

      <div>
        <div className={styles.mailboxHeader}>
          <h2 className={styles.mailboxHeaderH2}>
            Create Mailboxes for your purchased domains
          </h2>
        </div>
        <div className={styles.mailboxDescription}>
          You can auto-generate usernames or enter them manually.
        </div>
      </div>
      <div className={styles.infoBanner}>
        <svg
          className={styles.infoIcon}
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <circle cx="12" cy="12" r="10"></circle>
          <line x1="12" y1="16" x2="12" y2="12"></line>
          <line x1="12" y1="8" x2="12.01" y2="8"></line>
        </svg>
        <div className={styles.infoBannerContent}>
          <h4>Configure Mailboxes</h4>
          <p>
            Set up the mailboxes for your selected domain(s). These will be used
            for your outreach campaigns.
          </p>
          <p>Mailboxes typically become active within 30 minutes of setup.</p>
        </div>
      </div>

      <div className={styles.createMailboxesContainer}>
        {mailboxes.map((mb, domainIndex) => (
          <div
            key={mb.domain}
            className={`${styles.domainMailboxSection} ${
              openDomainIndex === domainIndex ? styles.accordionOpen : ""
            }`}
          >
            <div
              className={styles.accordionHeader}
              onClick={() => handleDomainHeaderClick(domainIndex)}
            >
              <div className={styles.accordionTitleContainer}>
                <div className={styles.accordionTitleGroup}>
                  <h3>Mailboxes for</h3>
                  <p className={styles.accordionDomainName}>{mb.domain}</p>
                </div>
                {viewMode[mb.domain] === "table" && (
                  <span className={styles.createdCountIndicator}>
                    <svg
                      width="12"
                      height="12"
                      viewBox="0 0 14 14"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      style={{ marginRight: "4px" }}
                    >
                      <path
                        d="M11.6663 3.5L5.24967 9.91667L2.33301 7"
                        stroke="#16A34A"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                    Mailboxes Created ({mb.config.length}/{INITIAL_SLOT_COUNT})
                  </span>
                )}
              </div>
              <div className={styles.accordionActions}>
                {openDomainIndex === domainIndex &&
                  viewMode[mb.domain] === "table" &&
                  mb.config.length < INITIAL_SLOT_COUNT && (
                    <button
                      type="button"
                      className={styles.addToCartBtnNew}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleAddMoreMailboxes(mb.domain);
                      }}
                      title="Add more mailboxes"
                    >
                      + Add More Mailboxes
                    </button>
                  )}
                <span
                  className={`${styles.accordionIcon} ${
                    openDomainIndex === domainIndex ? styles.iconOpen : ""
                  }`}
                >
                  <svg
                    width="10"
                    height="6"
                    viewBox="0 0 10 6"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M1 1L5 5L9 1"
                      stroke="currentColor"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </span>
              </div>
            </div>
            {/* Form or Table */}
            {openDomainIndex === domainIndex &&
              viewMode[mb.domain] === "form" && (
                <div className={styles.accordionContent}>
                  <div className={styles.tabContainer}>
                    <button
                      type="button"
                      className={`${styles.tabButton} ${
                        activeTab[mb.domain] === "auto" ? styles.tabActive : ""
                      }`}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleTabChange(mb.domain, "auto");
                      }}
                    >
                      Auto-Generate
                    </button>
                    <button
                      type="button"
                      className={`${styles.tabButton} ${
                        activeTab[mb.domain] === "manual"
                          ? styles.tabActive
                          : ""
                      }`}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleTabChange(mb.domain, "manual");
                      }}
                    >
                      Enter Manually
                    </button>
                  </div>

                  {activeTab[mb.domain] === "auto" && (
                    <div className={styles.autoGenerateSection}>
                      {" "}
                      {/* New wrapper for auto-gen content */}
                      <div className={styles.autoGenNameInputsRow}>
                        <div className={styles.formInputGroup}>
                          <label
                            htmlFor={`autoGenFirstName-${mb.domain}`}
                            className={styles.formLabel}
                          >
                            First Name
                          </label>
                          <input
                            type="text"
                            id={`autoGenFirstName-${mb.domain}`}
                            value={autoGen[mb.domain]?.firstName}
                            onChange={(e) =>
                              handleAutoGenFormInputChange(
                                mb.domain,
                                "firstName",
                                e.target.value
                              )
                            }
                            placeholder="First Name"
                            className={styles.formInput}
                          />
                        </div>
                        <div className={styles.formInputGroup}>
                          <label
                            htmlFor={`autoGenLastName-${mb.domain}`}
                            className={styles.formLabel}
                          >
                            Last Name
                          </label>
                          <input
                            type="text"
                            id={`autoGenLastName-${mb.domain}`}
                            value={autoGen[mb.domain]?.lastName}
                            onChange={(e) =>
                              handleAutoGenFormInputChange(
                                mb.domain,
                                "lastName",
                                e.target.value
                              )
                            }
                            placeholder="Last Name"
                            className={styles.formInput}
                          />
                        </div>
                      </div>
                      <div className={styles.usernameSuggestionArea}>
                        {autoGen[mb.domain]?.show &&
                        autoGen[mb.domain]?.suggestions.length > 0 ? (
                          <>
                            <div className={styles.suggestionsGrid}>
                              {autoGen[mb.domain]?.suggestions.map(
                                (suggestion) => (
                                  <div
                                    key={suggestion.id}
                                    className={styles.suggestionItem}
                                  >
                                    <input
                                      type="checkbox"
                                      className={styles.suggestionCheckbox}
                                      checked={suggestion.isChecked}
                                      onChange={() =>
                                        handleSuggestionCheckChange(
                                          mb.domain,
                                          suggestion.id
                                        )
                                      }
                                      id={`suggestion-checkbox-${mb.domain}-${suggestion.id}`}
                                    />
                                    <input
                                      type="text"
                                      className={styles.suggestionUsernameInput}
                                      value={suggestion.username}
                                      onChange={(e) =>
                                        handleSuggestedUsernameChange(
                                          mb.domain,
                                          suggestion.id,
                                          e.target.value
                                        )
                                      }
                                    />
                                    <span
                                      className={styles.suggestionDomainSuffix}
                                    >
                                      @{mb.domain}
                                    </span>
                                  </div>
                                )
                              )}
                            </div>
                          </>
                        ) : (
                          <div className={styles.suggestionAreaContent}>
                            <h5 className={styles.suggestionAreaTitle}>
                              Enter First and Last Name
                            </h5>
                            <p className={styles.suggestionAreaDescription}>
                              Enter a first and last name to see auto-generated
                              username suggestions.
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {activeTab[mb.domain] === "manual" && (
                    <div className={styles.manualMailboxEntryArea}>
                      {mb.config.map((mailbox, mailboxIndex) => (
                        <div
                          key={mailboxIndex}
                          className={styles.mailboxEntryRow}
                        >
                          <span className={styles.mailboxRowNumber}>
                            {mailboxIndex + 1}
                          </span>
                          <div className={styles.formInputGroup}>
                            <input
                              type="text"
                              value={mailbox.firstName}
                              onChange={(e) =>
                                handleMailboxInputChange(
                                  mb.domain,
                                  mailboxIndex,
                                  "firstName",
                                  e.target.value
                                )
                              }
                              placeholder="First Name"
                              className={styles.formInput}
                            />
                            {mailbox.errors?.firstName && (
                              <small className={styles.fieldError}>
                                {mailbox.errors.firstName}
                              </small>
                            )}
                          </div>
                          <div className={styles.formInputGroup}>
                            <input
                              type="text"
                              value={mailbox.lastName}
                              onChange={(e) =>
                                handleMailboxInputChange(
                                  mb.domain,
                                  mailboxIndex,
                                  "lastName",
                                  e.target.value
                                )
                              }
                              placeholder="Last Name"
                              className={styles.formInput}
                            />
                            {mailbox.errors?.lastName && (
                              <small className={styles.fieldError}>
                                {mailbox.errors.lastName}
                              </small>
                            )}
                          </div>
                          <div className={styles.formInputGroup}>
                            <input
                              type="text"
                              value={mailbox.username}
                              onChange={(e) =>
                                handleMailboxInputChange(
                                  mb.domain,
                                  mailboxIndex,
                                  "username",
                                  e.target.value
                                )
                              }
                              placeholder="Username"
                              className={styles.formInput}
                            />
                            {mailbox.errors?.username && (
                              <small className={styles.fieldError}>
                                {mailbox.errors.username}
                              </small>
                            )}
                          </div>
                          <span className={styles.mailboxDomainSuffix}>
                            @{mb.domain}
                          </span>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}

            {/* Table View */}
            {openDomainIndex === domainIndex &&
              viewMode[mb.domain] === "table" && (
                <div className={styles.mailboxDetailsTableContainer}>
                  <table className={styles.mailboxDetailsTable}>
                    <thead>
                      <tr>
                        <th>Mailboxes</th>
                        <th>User Name</th>
                        <th>Domain</th>
                        <th>Created on</th>
                        <th className={styles.actionsCell}>Action</th>
                      </tr>
                    </thead>
                    <tbody>
                      {mb.config.map((row, idx) => (
                        <tr key={idx}>
                          <td>
                            {row.username}@{mb.domain}
                          </td>
                          <td>
                            {row.firstName} {row.lastName}
                          </td>
                          <td>{mb.domain}</td>
                          <td>May 7, 2025</td>
                          <td className={styles.actionsCell}>
                            <button
                              type="button"
                              className={`${styles.actionIcon} ${styles.editIcon}`}
                              title="Edit"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleEditDomainMailboxes(mb.domain);
                              }}
                            >
                              <EditIcon />
                            </button>
                            <button
                              type="button"
                              className={`${styles.actionIcon} ${styles.deleteIcon}`}
                              title="Delete"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDeleteMailbox(mb.domain, idx);
                              }}
                            >
                              <DeleteIcon />
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            {/* Per-domain Create button */}
            {openDomainIndex === domainIndex &&
              viewMode[mb.domain] === "form" && (
                <div className={styles.domainActionFooter}>
                  <Button
                    type={BUTTON_TYPES.PRIMARY}
                    onClick={() => handleCreateSingleDomainMailboxes(mb.domain)}
                    disabled={(() => {
                      if (activeTab[mb.domain] === "auto") {
                        if (
                          autoGen[mb.domain]?.show &&
                          autoGen[mb.domain]?.suggestions.length > 0
                        ) {
                          return (
                            autoGen[mb.domain].suggestions.filter(
                              (s) => s.isChecked
                            ).length === 0
                          );
                        } else {
                          return true;
                        }
                      } else {
                        return !isAnyMailboxFilled(mb.config);
                      }
                    })()}
                  >
                    {getCreateActionText(mb.domain)}
                  </Button>
                </div>
              )}
          </div>
        ))}
        <div className={styles.createMailboxesFooter}>
          <Button
            type={BUTTON_TYPES.PRIMARY}
            disabled={!areAllDomainsMailboxesCreated()}
            onClick={handleCompleteSetup}
          >
            Complete Setup
          </Button>
        </div>
      </div>
    </div>
  );
};

export default CreateMailboxesPage;
