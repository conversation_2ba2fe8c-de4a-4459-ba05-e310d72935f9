import React from 'react';
import styles from '../style.module.css';
import { Domain } from './types';

interface DomainCartProps {
  selectedDomains: Domain[];
  removeDomain: (domainName: string) => void;
  domainCount: number;
}

const DomainCart = ({ selectedDomains, removeDomain, domainCount }: DomainCartProps) => {
  if (selectedDomains.length === 0) {
    return null;
  }

  return (
    <div className={styles.miniCart}>
      <div className={styles.miniCartHeader}>
        <h3>Selected Domains</h3>
      </div>
      <div className={styles.selectedDomainBubbleContainer}>
        {selectedDomains.map((domain) => (
          <div key={domain.domainName} className={styles.domainBubble}>
            <span className={styles.domainBubbleName}>{domain.domainName}</span>
            <button
              className={styles.removeDomainBubbleBtn}
              onClick={() => removeDomain(domain.domainName)}
              aria-label={`Remove ${domain.domainName}`}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round">
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </button>
          </div>
        ))}
      </div>
    </div>
  );
};

export default DomainCart;
