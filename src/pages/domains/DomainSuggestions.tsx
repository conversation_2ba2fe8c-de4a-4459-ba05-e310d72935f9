import React from 'react';
import { Domain } from './types';
import styles from '../style.module.css';


interface DomainSuggestionsProps {
  suggestedDomains: Domain[];
  selectedDomains: Domain[];
  addDomain: (domain: Domain) => void;
}

const DomainSuggestions = ({ suggestedDomains, selectedDomains, addDomain }: DomainSuggestionsProps) => {
  return (
    <div className="suggestions-section">
      <div className="suggestions-header">
        <h3>Popular Domain Suggestions</h3>
      </div>
      
      <div className="suggestions-content">
        {suggestedDomains.map((domain: Domain, index: number) => (
          <div key={index} className="suggestion-card">
            <div className="suggestion-header">
              <h4>{domain.domainName}</h4>
              <span
                className={
                  `${styles.availabilityTag} ${domain.available ? styles.available : styles.unavailable}`
                }
              >
                {domain.available ? 'Available' : 'Unavailable'}
              </span>
            </div>
            <div className="suggestion-footer">
              {/* <p>{domain.premium ? 'Premium domain' : 'Standard domain'}</p> */}
              <button 
                  className="add-domain-button"
                  disabled={selectedDomains.some(d => d.domainName === domain.domainName) || selectedDomains.length >= 2}
                  onClick={() => addDomain(domain)}
                >
                  Add to Cart
                </button>
              {/* {domain.available ? (
                <button 
                  className="add-domain-button"
                  disabled={selectedDomains.some(d => d.name === domain.name) || selectedDomains.length >= 2}
                  onClick={() => addDomain(domain)}
                >
                  Add to Cart
                </button>
              ) : (
                <button className="add-domain-button" disabled>
                  Unavailable
                </button>
              )} */}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default DomainSuggestions;
