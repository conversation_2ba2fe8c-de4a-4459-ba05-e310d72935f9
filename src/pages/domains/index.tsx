import React, { useCallback, useState } from 'react';
import { Domain } from './types';

import CreateMailboxesPage from './CreateMailboxes';
import DomainSearch from './DomainSearch';
import type { CompletedDomainSetup } from './SetupCompletePage';
import SetupCompletePage from './SetupCompletePage';
import "./style.css";
import { DOMAIN_PAGES } from '../RootContainer';

// interface DomainsPageProps {
//   onDomainsPurchased?: () => void;
//   onDomainsPurchasedWithData?: (completedConfigs: CompletedDomainSetup[]) => void;
// }

interface DomainsPageProps {
  setCurrentPage: (page: DOMAIN_PAGES) => void;
}

enum DOMAIN_PAGES_CURRENT_VIEW {
  DOMAIN_SELECTION, 
  MAILBOX_CREATION ,
  COMPLETE_SETUP 
}

const DomainsPage: React.FC<DomainsPageProps> = ({ setCurrentPage }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState<Domain[]>([]);
  const [selectedDomains, setSelectedDomains] = useState<Domain[]>([]);
  // isModalOpen is used in the commented out PurchaseProgressModal
  const [, setIsModalOpen] = useState(false);
  const [suggestedDomains, setSuggestedDomains] = useState<Domain[]>([]);

  const [currentView, setCurrentView] = useState<DOMAIN_PAGES_CURRENT_VIEW>(DOMAIN_PAGES_CURRENT_VIEW.DOMAIN_SELECTION);
  const [completedMailboxConfigs, setCompletedMailboxConfigs] = useState<CompletedDomainSetup[]>([]);

  const domainCount = selectedDomains.length;

  const clearResults = useCallback(() => setSearchResults([]), []);

  const addDomain = useCallback((domain: Domain) => {
    setSelectedDomains(prev =>
      prev.length < 2 && !prev.some(d => d.domainName === domain.domainName)
        ? [...prev, domain]
        : prev
    );
  }, []);

  const removeDomain = useCallback((domainName: string) => {
    setSelectedDomains(prev => prev.filter(domain => domain.domainName !== domainName));
  }, []);

  const handleCompleteSetup = (completedConfigs: CompletedDomainSetup[]) => {
    setCompletedMailboxConfigs(completedConfigs);
    setCurrentView(DOMAIN_PAGES_CURRENT_VIEW.COMPLETE_SETUP);
   
  };

  const handleInitiatePurchase = () => {
      setCurrentView(DOMAIN_PAGES_CURRENT_VIEW.MAILBOX_CREATION);
  };

  // Render the appropriate view based on the current state
  const renderView = () => {
    switch (currentView) {
      case DOMAIN_PAGES_CURRENT_VIEW.DOMAIN_SELECTION:
        return (
          <DomainSearch
            searchTerm={searchTerm}
            setSearchTerm={setSearchTerm}
            searchResults={searchResults}
            setSearchResults={setSearchResults}
            selectedDomains={selectedDomains}
            addDomain={addDomain}
            removeDomain={removeDomain}
            domainCount={domainCount}
            clearResults={clearResults}
            setIsModalOpen={setIsModalOpen}
            onProceedWithSelectedDomains={handleInitiatePurchase}
            suggestedDomains={suggestedDomains}
            setSuggestedDomains={setSuggestedDomains}
          />
        );
      case DOMAIN_PAGES_CURRENT_VIEW.MAILBOX_CREATION:
        return (
          <CreateMailboxesPage
            selectedDomainsFromSearch={selectedDomains}
            onCompleteSetup={handleCompleteSetup}
          />
        );
      case DOMAIN_PAGES_CURRENT_VIEW.COMPLETE_SETUP:
        return (
          <SetupCompletePage
            completedMailboxConfigs={completedMailboxConfigs}
            setCurrentPage={setCurrentPage}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="domains-page">
      {renderView()}
    </div>
  );
};

export default DomainsPage;

