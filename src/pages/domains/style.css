/* Plain CSS for domain management app */
:root {
    --primary-50: #eef2ff;
    --primary-100: #e0e7ff;
    --primary-200: #c7d2fe;
    --primary-300: #a5b4fc;
    --primary-400: #818cf8;
    --primary-500: #6366f1;
    --primary-600: #4f46e5;
    --primary-700: #4338ca;
    --primary-800: #3730a3;
    --primary-900: #312e81;
  
    --secondary-50: #fff1f2;
    --secondary-100: #ffe4e6;
    --secondary-200: #fecdd3;
    --secondary-300: #fda4af;
    --secondary-400: #fb7185;
    --secondary-500: #f43f5e;
    --secondary-600: #e11d48;
    --secondary-700: #be123c;
    --secondary-800: #9f1239;
    --secondary-900: #881337;
  
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
  
    --amber-50: #fffbeb;
    --amber-100: #fef3c7;
    --amber-500: #f59e0b;
  
    --green-100: #dcfce7;
    --green-600: #16a34a;
    --green-800: #166534;
  
    --red-100: #fee2e2;
    --red-800: #991b1b;
    
    /* Adding missing blue variables */
    --blue-50: #f0f9ff;
    --blue-100: #e0f2fe;
    --blue-600: #0284c7;
    
    /* Adding missing purple variables for search history */
    --purple-100: #f3e8ff;
    --pink-100: #fce7f3;
    --cyan-100: #cffafe;
    --indigo-100: #e0e7ff;
    --lime-100: #ecfccb;
    --orange-100: #ffedd5;
  }
  
  body {
    font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: var(--gray-50);
    color: var(--gray-800);
  }
  
  * {
    box-sizing: border-box;
  }
  
  /* Layout styles */
  .app-container {
    display: flex;
    height: 100vh;
    overflow: hidden;
  }
  
  /* Sidebar styles */
  .sidebar {
    width: 64px;
    background-color: white;
    border-right: 1px solid var(--gray-200);
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 16px 0;
  }
  
  .sidebar-logo {
    width: 32px;
    height: 32px;
    background-color: var(--primary-100);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-600);
    margin-bottom: 24px;
  }
  
  .sidebar-items {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 24px;
  }
  
  .sidebar-item {
    position: relative;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    color: var(--gray-500);
    cursor: pointer;
    transition: all 0.2s;
  }
  
  .sidebar-item:hover {
    background-color: var(--gray-100);
  }
  
  .sidebar-item.active {
    color: var(--primary-600);
    background-color: var(--gray-100);
  }
  
  .sidebar-item.active::before {
    content: '';
    position: absolute;
    left: -8px;
    top: 0;
    height: 100%;
    width: 4px;
    background-color: var(--primary-600);
  }
  
  /* Main content styles */
  .main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
  
  /* Header styles */
  .header {
    background-color: white;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 24px;
  }
  
  .header h1 {
    font-size: 20px;
    font-weight: 600;
    color: var(--gray-800);
    margin: 0;
  }
  
  .header-user {
    display: flex;
    align-items: center;
    color: var(--gray-700);
    cursor: pointer;
  }
  
  .header-user span {
    margin-right: 8px;
  }
  
  /* Tab navigation styles */
  .tab-navigation {
    background-color: white;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
  }
  
  .tab-button {
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 500;
    color: var(--gray-500);
    border: none;
    background: none;
    cursor: pointer;
    transition: color 0.2s;
  }
  
  .tab-button:hover {
    color: var(--gray-700);
  }
  
  .tab-button.active {
    color: var(--primary-600);
    border-bottom: 2px solid var(--primary-500);
  }
  
  /* Content wrapper */
  .content-wrapper {
    flex: 1;
    overflow: auto;
    padding: 24px;
  }
  
  /* Domain search section */
  .domain-search {
    margin-bottom: 32px;
  }
  
  .search-container {
    position: relative;
  }
  
  .search-box {
    display: flex;
  }
  
  .search-input-container {
    position: relative;
    flex-grow: 1;
  }
  
  .search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-400);
  }
  
  .search-input {
    width: 100%;
    padding: 8px 12px 8px 36px;
    border: 1px solid var(--gray-300);
    border-radius: 6px;
    font-size: 14px;
    line-height: 20px;
    background-color: white;
    color: var(--gray-800);
  }
  
  .search-input:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 2px var(--primary-100);
  }
  
  .search-input::placeholder {
    color: var(--gray-500);
  }
  
  .search-button {
    margin-left: 12px;
    display: inline-flex;
    align-items: center;
    padding: 8px 16px;
    border: 1px solid transparent;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    background-color: var(--primary-600);
    color: white;
    cursor: pointer;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: background-color 0.2s;
  }
  
  .search-button:hover {
    background-color: var(--primary-700);
  }
  
  .search-button svg {
    margin-right: 8px;
  }
  
  /* Removing duplicate definition */
  
  .domain-result {
    padding: 12px;
    border-bottom: 1px solid var(--gray-100);
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
  }
  
  .domain-result:last-child {
    border-bottom: none;
  }
  
  .domain-result:hover {
    background-color: var(--gray-50);
  }
  
  .domain-result-info h4 {
    font-size: 14px;
    font-weight: 500;
    color: var(--gray-800);
    margin: 0 0 2px 0;
  }
  
  .domain-result-info p {
    font-size: 12px;
    color: var(--gray-500);
    margin: 0;
  }
  
  .domain-result-action button {
    display: inline-flex;
    align-items: center;
    padding: 4px 12px;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    background-color: var(--primary-100);
    color: var(--primary-600);
    cursor: pointer;
    transition: background-color 0.2s;
  }
  
  .domain-result-action button:hover {
    background-color: var(--primary-200);
  }
  
  .domain-result-action button:disabled {
    background-color: var(--gray-100);
    color: var(--gray-400);
    cursor: not-allowed;
  }
  
  .domain-result-action .unavailable {
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 12px;
    background-color: var(--red-100);
    color: var(--red-800);
  }
  
  /* Mini Cart Section */
  .mini-cart {
    background-color: white;
    border: 1px solid var(--primary-200);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    margin: 16px 0 24px;
    animation: slideIn 0.3s ease-out;
  }
  
  @keyframes slideIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .mini-cart-header {
    padding: 12px 16px;
    background-color: var(--primary-50);
    border-bottom: 1px solid var(--primary-100);
  }
  
  .mini-cart-header h3 {
    font-size: 15px;
    font-weight: 500;
    color: var(--gray-800);
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  
  .domain-count {
    font-size: 13px;
    color: var(--primary-700);
    background-color: var(--primary-100);
    padding: 3px 8px;
    border-radius: 12px;
    font-weight: 500;
  }
  
  .mini-cart-items {
    padding: 10px;
  }
  
  .mini-cart-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 10px;
    border-radius: 6px;
    margin-bottom: 6px;
    transition: background-color 0.2s;
  }
  
  .mini-cart-item:hover {
    background-color: var(--gray-50);
  }
  
  .mini-cart-item:last-child {
    margin-bottom: 0;
  }
  
  .mini-cart-item .domain-name {
    font-size: 14px;
    font-weight: 500;
    color: var(--gray-800);
  }
  
  .mini-remove-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 22px;
    height: 22px;
    color: var(--gray-500);
    background: transparent;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s;
    padding: 0;
  }
  
  .mini-remove-button:hover {
    color: var(--red-800);
    background-color: var(--red-100);
  }
  
  .checkout-button {
    width: calc(100% - 20px);
    margin: 4px 10px 12px;
    padding: 8px 0;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    background-color: var(--primary-600);
    color: white;
    cursor: pointer;
    transition: all 0.2s;
  }
  
  .checkout-button:hover {
    background-color: var(--primary-700);
  }
  
  /* Domain Search Enhancements */
  .domains-page {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }
  
  .domain-search-form {
    width: 100%;
  }
  
  .search-input-wrapper {
    display: flex;
    width: 100%;
    position: relative;
    margin-bottom: 16px;
  }
  
  .search-input {
    flex: 1;
    padding: 12px 12px 12px 40px;
    font-size: 15px;
    border: 1px solid var(--gray-300);
    border-radius: 6px 0 0 6px;
    transition: all 0.2s;
  }
  
  .search-input:focus {
    outline: none;
    border-color: var(--primary-400);
    box-shadow: 0 0 0 2px var(--primary-100);
  }
  
  .search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-400);
    z-index: 5;
  }
  
  .search-results-section {
    display: flex;
    flex-direction: column;
  }
  
  .search-button {
    min-width: 100px;
    padding: 0 16px;
    border: none;
    background-color: var(--primary-600);
    color: white;
    font-weight: 500;
    font-size: 14px;
    border-radius: 0 6px 6px 0;
    cursor: pointer;
    transition: background-color 0.2s;
  }
  
  .search-button:hover:not(:disabled) {
    background-color: var(--primary-700);
  }
  
  .search-button:disabled {
    background-color: var(--gray-400);
    cursor: not-allowed;
  }
  
  .domain-cart-counter {
    display: flex;
    justify-content: flex-end;
    color: var(--gray-600);
    font-size: 14px;
    margin-bottom: 16px;
    padding-right: 8px;
  }
  
  .domain-results-grid {
    background-color: white;
    border-radius: 8px;
    margin-bottom: 24px;
    overflow: hidden;
  }
  
  .results-header {
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .results-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    color: var(--gray-800);
    display: none; /* Hide this as per the design */
  }
  
  .results-count {
    font-size: 14px;
    color: var(--gray-600);
    padding: 4px 10px;
    font-weight: 500;
    display: none; /* Hide this as per the design */
  }
  
  .results-container {
    padding: 0;
  }
  
  .results-grid {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
  
  /* Domain availability message - new style from image */
  .domain-availability-message {
    display: flex;
    align-items: center;
    color: var(--green-600);
    font-size: 14px;
    margin-bottom: 16px;
    gap: 8px;
  }
  
  .domain-availability-message svg {
    color: var(--green-600);
  }
  
  /* Domain card - redesigned to match the image */
  .domain-card {
    padding: 16px;
    border-radius: 8px;
    background-color: var(--blue-50);
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: all 0.2s;
    border: 2px solid transparent;
    position: relative;
    cursor: pointer;
  }
  
  .domain-card.selected {
    border: 2px solid var(--primary-500);
    background-color: var(--blue-100);
  }
  
  .domain-info {
    display: flex;
    align-items: center;
    margin-bottom: 0;
  }
  
  .checkbox-button {
    margin-right: 12px;
    width: 22px;
    height: 22px;
    cursor: pointer;
    padding: 0;
    background: none;
    border: none;
    position: relative;
  }
  
  .checkbox-button:focus {
    outline: 2px solid var(--primary-300);
    border-radius: 4px;
  }
  
  .simple-checkbox {
    width: 22px;
    height: 22px;
    border: 2px solid var(--gray-300);
    border-radius: 4px;
    background-color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
    pointer-events: none;
  }
  
  .simple-checkbox.checked {
    background-color: var(--primary-600);
    border-color: var(--primary-600);
  }
  
  .simple-checkbox svg {
    width: 14px;
    height: 14px;
  }
  
  .domain-text-info {
    display: flex;
    align-items: center;
  }
  
  .domain-name {
    font-size: 15px;
    font-weight: 500;
    color: var(--gray-800);
    margin: 0;
  }
  
  .domain-status {
    margin-left: 10px;
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
  }
  
  .domain-status.available {
    background-color: var(--blue-100);
    color: var(--blue-600);
  }
  
  .domain-status.unavailable {
    background-color: var(--red-100);
    color: var(--red-800);
  }
  
  .domain-actions {
    margin-top: 0;
    position: relative;
    z-index: 10;
    pointer-events: auto;
  }
  
  .add-domain-btn {
    padding: 8px 16px;
    background-color: var(--primary-600);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 6px;
    position: relative;
    z-index: 10; /* Higher z-index to ensure clickability */
    pointer-events: auto; /* Explicitly enable pointer events */
  }
  
  .add-domain-btn:hover:not(:disabled) {
    background-color: var(--primary-700);
  }
  
  .add-domain-btn svg {
    width: 16px;
    height: 16px;
  }
  
  .add-domain-btn:disabled {
    background-color: var(--gray-400);
    cursor: not-allowed;
  }
  
  .unavailable-btn {
    padding: 8px 12px;
    background-color: var(--gray-100);
    color: var(--gray-500);
    border: 1px solid var(--gray-200);
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: not-allowed;
    opacity: 0.8;
    text-decoration: line-through;
  }
  
  /* Action buttons at bottom */
  .domain-search-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
  }
  
  .clear-search-btn {
    padding: 10px 16px;
    background-color: white;
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
  }
  
  .continue-btn {
    padding: 10px 16px;
    background-color: var(--blue-200);
    color: var(--gray-700);
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
  }
  
  .no-results,
  .search-results-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 32px 16px;
    text-align: center;
    color: var(--gray-500);
  }
  
  .no-results svg,
  .search-results-message svg {
    color: var(--gray-400);
    margin-bottom: 16px;
  }
  
  .no-results h4,
  .search-results-message h4 {
    font-size: 16px;
    font-weight: 500;
    color: var(--gray-700);
    margin: 0 0 8px 0;
  }
  
  .no-results p,
  .search-results-message p,
  .searching-message p {
    font-size: 14px;
    margin: 0;
  }
  
  .searching-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 32px 16px;
    text-align: center;
    color: var(--gray-500);
  }
  
  /* Domain Search Dropdown Styles */
  .domain-dropdown {
    position: absolute;
    top: calc(100% + 4px);
    left: 0;
    right: 0;
    width: 100%;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 8px 16px rgba(0,0,0,0.15);
    z-index: 1000;
    overflow: hidden;
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid var(--gray-200);
    animation: dropdownFadeIn 0.2s ease forwards;
  }
  
  @keyframes dropdownFadeIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .domain-dropdown-header {
    display: grid;
    grid-template-columns: 1.5fr 1fr 1fr;
    padding: 12px 16px;
    background-color: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
  }
  
  .domain-dropdown-header span {
    font-size: 13px;
    font-weight: 600;
    color: var(--gray-600);
  }
  
  .domain-dropdown-list {
    max-height: 350px;
    overflow-y: auto;
  }
  
  .domain-dropdown-item {
    display: grid;
    grid-template-columns: 1.5fr 1fr 1fr;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid var(--gray-100);
    transition: background-color 0.2s;
  }
  
  .domain-dropdown-item:hover {
    background-color: var(--gray-50);
  }
  
  .domain-dropdown-item:last-child {
    border-bottom: none;
  }
  
  .domain-name {
    font-weight: 500;
    color: var(--gray-800);
  }
  
  .domain-availability {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .available-tag {
    display: inline-block;
    padding: 4px 8px;
    background-color: var(--green-100);
    color: var(--green-600);
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
  }
  
  .unavailable-tag {
    display: inline-block;
    padding: 4px 8px;
    background-color: var(--red-100);
    color: var(--red-800);
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
  }
  
  .premium-tag {
    display: inline-block;
    padding: 4px 8px;
    background-color: var(--amber-100);
    color: var(--amber-600);
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
  }
  
  .add-to-cart-btn {
    padding: 6px 12px;
    background-color: var(--primary-600);
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
    white-space: nowrap;
  }
  
  .add-to-cart-btn:hover:not(:disabled) {
    background-color: var(--primary-700);
  }
  
  .add-to-cart-btn:disabled {
    background-color: var(--gray-400);
    cursor: not-allowed;
  }
  
  .added-btn {
    padding: 6px 12px;
    background-color: var(--green-600);
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 13px;
    font-weight: 500;
    cursor: default;
  }
  
  .unavailable-btn {
    padding: 6px 12px;
    background-color: var(--gray-200);
    color: var(--gray-500);
    border: 1px solid var(--gray-300);
    border-radius: 4px;
    font-size: 13px;
    font-weight: 500;
    cursor: not-allowed;
    opacity: 0.8;
    text-decoration: line-through;
  }
  
  /* Make all disabled buttons clearly appear disabled */
  button:disabled {
    opacity: 0.7 !important;
    cursor: not-allowed !important;
    pointer-events: none !important;
  }
  
  .no-domains-message {
    padding: 24px 16px;
    text-align: center;
    color: var(--gray-500);
  }
  
  .no-domains-message p {
    margin: 4px 0;
    font-size: 14px;
  }
  
  .search-container {
    position: relative;
    margin-bottom: 20px;
    z-index: 10; /* Ensure dropdown appears on top */
  }
  
  /* Purchase Progress Modal -- Styles to be removed */
  /*
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(55, 65, 81, 0.75);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 50;
  }
  
  .modal-hidden {
    display: none;
  }
  
  .modal-container {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    max-width: 32rem;
    width: 100%;
    overflow: hidden;
    margin: 0 16px;
  }
  
  .modal-header {
    padding: 16px 24px;
    border-bottom: 1px solid var(--gray-200);
    background-color: var(--gray-50);
  }
  
  .modal-header h3 {
    font-size: 16px;
    font-weight: 500;
    color: var(--gray-800);
    margin: 0;
  }
  
  .modal-content {
    padding: 24px;
  }
  
  .loader-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-bottom: 24px;
  }
  
  .loader {
    width: 48px;
    height: 48px;
    border: 5px solid var(--primary-500);
    border-bottom-color: transparent;
    border-radius: 50%;
    display: inline-block;
    box-sizing: border-box;
    animation: rotation 1s linear infinite;
    margin-bottom: 16px;
  }
  
  @keyframes rotation {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
  
  .loader-text {
    color: var(--gray-700);
    margin-bottom: 8px;
    text-align: center;
  }
  
  .loader-subtext {
    font-size: 14px;
    color: var(--gray-500);
    text-align: center;
  }
  
  .progress-steps {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }
  
  .progress-step {
    display: flex;
    flex-direction: column;
  }
  
  .progress-step-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
  }
  
  .progress-step-label {
    font-size: 14px;
    font-weight: 500;
    color: var(--gray-700);
  }
  
  .progress-step-status {
    font-size: 14px;
  }
  
  .status-completed {
    color: var(--green-600);
  }
  
  .status-in-progress {
    color: var(--primary-600);
  }
  
  .status-pending {
    color: var(--gray-400);
  }
  
  .progress-bar {
    height: 6px;
    background-color: var(--gray-200);
    border-radius: 3px;
    overflow: hidden;
  }
  
  .progress-bar-fill {
    height: 100%;
    background-color: var(--primary-500);
    border-radius: 3px;
    transition: width 0.5s ease-in-out;
  }
  
  .modal-footer {
    padding: 16px 24px;
    border-top: 1px solid var(--gray-200);
    background-color: var(--gray-50);
    display: flex;
    justify-content: flex-end;
  }
  
  .cancel-button {
    display: inline-flex;
    align-items: center;
    padding: 8px 16px;
    border: 1px solid var(--gray-300);
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    background-color: white;
    color: var(--gray-700);
    cursor: pointer;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: background-color 0.2s;
  }
  
  .cancel-button:hover {
    background-color: var(--gray-50);
  }
  */
  
  /* Mailboxes page */
  .steps-container {
    display: flex;
    justify-content: center;
    margin: 30px auto 40px;
    position: relative;
    max-width: 400px;
    width: 90%;
  }
  
  .steps-line {
    position: absolute;
    top: 24px;
    left: 50%;
    transform: translateX(-50%);
    height: 2px;
    background-color: var(--gray-300);
    width: 70%;
    z-index: 1;
  }
  
  .step {
    width: 33.33%;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
  }
  
  .step-circle {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background-color: var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    margin-bottom: 12px;
    color: var(--gray-600);
  }
  
  .step-circle.active {
    background-color: var(--primary-500);
    color: white;
  }
  
  .step-label {
    font-size: 14px;
    color: var(--gray-600);
    text-align: center;
  }
  
  .mailbox-container {
    background-color: white;
    border-radius: 8px;
    padding: 32px 48px;
    width: 92%;
    max-width: 1000px;
    margin: 0 auto;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }
  
  .mailbox-header {
    margin-bottom: 24px;
    text-align: center;
  }
  
  .mailbox-header h2 {
    font-size: 22px;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 12px;
  }
  
  .mailbox-description {
    font-size: 15px;
    color: var(--gray-600);
    margin-bottom: 24px;
    max-width: 650px;
  }
  
  .mailbox-search {
    margin-bottom: 24px;
  }
  
  .mailbox-tips {
    background-color: var(--primary-50);
    border-radius: 8px;
    padding: 16px 20px;
    margin-bottom: 24px;
    border: 1px solid var(--primary-100);
  }
  
  .tip-title {
    display: flex;
    align-items: center;
    color: var(--primary-700);
    margin-bottom: 10px;
    font-weight: 500;
    font-size: 14px;
  }
  
  .tip-title svg {
    margin-right: 8px;
    color: var(--primary-500);
  }
  
  .tip-list {
    list-style: none;
    padding: 0;
    margin: 0;
  }
  
  .tip-item {
    display: flex;
    margin-bottom: 10px;
  }
  
  .tip-item:last-child {
    margin-bottom: 0;
  }
  
  .tip-bullet {
    color: var(--primary-400);
    margin-right: 8px;
  }
  
  .tip-content {
    flex: 1;
  }
  
  .tip-content strong {
    display: block;
    margin-bottom: 2px;
    color: var(--gray-700);
    font-size: 13px;
  }
  
  .tip-content p {
    color: var(--gray-600);
    margin: 0;
    font-size: 13px;
    line-height: 1.4;
  }
  
  .domain-search-input {
    position: relative;
    margin-bottom: 8px;
    display: flex;
  }
  
  .domain-search-input input {
    flex: 1;
    padding: 14px 16px;
    border: 1px solid var(--gray-300);
    border-radius: 10px 0 0 10px;
    font-size: 16px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
    transition: all 0.2s ease;
    border-right: none;
  }
  
  .domain-search-input input:focus {
    outline: none;
    border-color: var(--primary-400);
    box-shadow: 0 0 0 2px var(--primary-100);
    z-index: 1;
  }
  
  .domain-search-input svg {
    position: absolute;
    top: 50%;
    left: 16px;
    transform: translateY(-50%);
    color: var(--gray-400);
    z-index: 2;
  }
  
  .domain-search-input input {
    padding-left: 44px;
  }
  
  .search-button {
    background-color: var(--primary-600);
    color: white;
    border: none;
    border-radius: 0 10px 10px 0;
    padding: 0 20px;
    font-size: 15px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
  }
  
  .search-button:hover {
    background-color: var(--primary-700);
  }
  
  .domain-results-dropdown {
    margin-top: 0;
    border: 1px solid var(--gray-300);
    border-radius: 0 0 10px 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    background-color: white;
    overflow: hidden;
    max-height: 350px;
    overflow-y: auto;
    z-index: 10;
    width: 100%;
  }
  
  .domain-result-item {
    padding: 14px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.2s ease;
    border-bottom: 1px solid var(--gray-200);
    cursor: pointer;
  }
  
  .domain-result-item:last-child {
    border-bottom: none;
  }
  
  .domain-result-item:hover {
    background-color: var(--gray-50);
  }
  
  .domain-result-item.selected {
    background-color: var(--primary-50);
    border-color: var(--primary-200);
  }
  
  .domain-info {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .domain-name {
    font-size: 16px;
    font-weight: 500;
    color: var(--gray-800);
  }
  
  .custom-checkbox {
    position: relative;
    width: 20px;
    height: 20px;
  }
  
  .custom-checkbox input {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
  }
  
  .custom-checkbox label {
    position: absolute;
    top: 0;
    left: 0;
    width: 20px;
    height: 20px;
    background-color: white;
    border: 2px solid var(--primary-300);
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
  }
  
  .custom-checkbox label.checked {
    background-color: var(--primary-600);
    border-color: var(--primary-600);
    color: white;
  }
  
  .no-results {
    padding: 20px;
    text-align: center;
    color: var(--gray-600);
  }
  
  .no-results p {
    margin: 0 0 8px;
    font-weight: 500;
  }
  
  .no-results-suggestion {
    color: var(--gray-500);
    font-size: 14px;
  }
  
  .domain-available-tag {
    background-color: var(--green-100);
    color: var(--green-800);
    padding: 5px 10px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
    border: 1px solid var(--green-200);
  }
  
  .add-to-cart-btn {
    background-color: var(--primary-600);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    transition: all 0.2s ease;
    white-space: nowrap;
    min-width: 120px;
    justify-content: center;
  }
  
  .add-to-cart-btn svg {
    margin-right: 8px;
  }
  
  .add-to-cart-btn:hover {
    background-color: var(--primary-700);
  }
  
  .add-to-cart-btn:active {
    transform: translateY(1px);
  }
  
  .add-to-cart-btn.in-cart {
    background-color: var(--green-600);
    cursor: default;
  }
  
  .add-to-cart-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
  
  /* Original placeholder styles */
  .placeholder-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 64px;
    text-align: center;
  }
  
  .placeholder-icon {
    font-size: 48px;
    color: var(--gray-300);
    margin-bottom: 16px;
  }
  
  .placeholder-title {
    font-size: 20px;
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: 8px;
  }
  
  .placeholder-text {
    font-size: 16px;
    color: var(--gray-500);
    max-width: 400px;
  }
  
  /* Responsive adjustments */
  @media (max-width: 768px) {
    .suggestions-content {
      grid-template-columns: 1fr;
    }
    
    .search-box {
      flex-direction: column;
    }
    
    .search-button {
      margin-left: 0;
      margin-top: 8px;
      width: 100%;
    }
    
    .cart-footer {
      flex-direction: column;
      gap: 16px;
    }
    
    .continue-button {
      width: 100%;
    }
  }
  
  /* Search History Visualizer styles */
  /*
  .search-history-container {
    margin: 20px 0;
    padding: 12px 16px;
    background-color: var(--gray-50);
    border-radius: 8px;
    border: 1px solid var(--gray-200);
  }
  
  .search-history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
  }
  
  .search-history-header h3 {
    font-size: 14px;
    font-weight: 500;
    color: var(--gray-700);
    margin: 0;
  }
  
  .clear-history-btn {
    font-size: 12px;
    color: var(--gray-500);
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px 8px;
    transition: all 0.2s;
  }
  
  .clear-history-btn:hover {
    color: var(--gray-700);
    background-color: var(--gray-200);
    border-radius: 4px;
  }
  
  .search-history-bubbles {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .history-bubble {
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 13px;
    color: var(--gray-700);
    cursor: pointer;
    transition: transform 0.2s, box-shadow 0.2s;
    animation: bubbleFloat 0.5s ease-out backwards;
    border: 1px solid var(--gray-200);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  }
  
  .history-bubble:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }
  
  @keyframes bubbleFloat {
    0% {
      opacity: 0;
      transform: translateY(10px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }
  */
  
  /* Styles from CreateMailboxes.css */
  .mailbox-creation-page {
    max-width: 800px; /* Adjust max-width as needed */
    margin: 30px auto;
    padding: 0 15px; /* Add horizontal padding */
  }
  
  /* Info Banner */
  /*
  .info-banner {
    display: flex;
    align-items: flex-start; 
    background-color: var(--blue-50); 
    border: 1px solid var(--blue-100);
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 32px;
    color: var(--blue-800); 
  }
  
  .info-banner .info-icon {
    color: var(--blue-500); 
    margin-right: 12px;
    flex-shrink: 0; 
    margin-top: 2px; 
  }
  
  .info-banner-content h4 {
    font-size: 15px;
    font-weight: 600;
    color: var(--blue-900); 
    margin: 0 0 8px 0;
  }
  
  .info-banner-content p {
    font-size: 14px;
    color: var(--blue-700); 
    margin: 0 0 4px 0;
    line-height: 1.5;
  }
  .info-banner-content p:last-child {
    margin-bottom: 0;
  }
  */
  
  /* Section Header */
  /*
  .mailbox-section-header {
    margin-bottom: 24px;
  }
  
  .mailbox-section-header h2 {
    font-size: 20px;
    font-weight: 600;
    color: var(--gray-800);
    margin: 0 0 4px 0;
  }
  
  .mailbox-section-header p {
    font-size: 14px;
    color: var(--gray-600);
    margin: 0;
  }
  */
  
  .header-actions {
    display: flex;
    gap: 12px;
  }
  
  /*
  .btn { 
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid transparent;
  }
  */
  
  .btn-secondary {
    background-color: white;
    color: var(--gray-700);
    border-color: var(--gray-300);
    box-shadow: 0 1px 2px rgba(0,0,0,0.05);
  }
  
  .btn-secondary:hover {
    background-color: var(--gray-50);
  }
  
  /*
  .btn-primary {
    background-color: var(--primary-600);
    color: white;
    border-color: var(--primary-600);
  }
  .btn-primary:hover {
    background-color: var(--primary-700);
  }
  */
  
  .btn-with-icon {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  .btn-with-icon svg {
    width: 18px; 
    height: 18px;
  }
  
  /* Disabled look for Auto Fill button */
  .btn-disabled-look {
    opacity: 0.6;
    cursor: not-allowed;
    color: var(--gray-400);
    border-color: var(--gray-200);
  }
  .btn-disabled-look svg {
   color: var(--gray-400);
  }
  
  /* Form Container */
  /*
  .mailbox-form-container {
    background-color: white;
    border: 1px solid var(--gray-200);
    border-radius: 8px;
    padding: 24px 32px 32px 32px; 
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  }
  
  .mailbox-form-container h3 {
    font-size: 16px;
    font-weight: 500;
    color: var(--gray-800);
    margin: 0 0 24px 0;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--gray-200);
  }
  */
  
  /* Form Elements */
  /*
  .form-row {
    display: flex;
    gap: 24px; 
    margin-bottom: 20px;
  }
  
  .form-group {
    display: flex;
    flex-direction: column;
    margin-bottom: 20px;
    flex-grow: 1; 
  }
  .form-group.form-group-fname {
    flex-grow: 1; 
  }
  .form-row > .form-group {
    margin-bottom: 0; 
  }
  
  
  .form-group label {
    font-size: 14px;
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: 6px;
  }
  
  .required-asterisk {
    color: var(--secondary-500); 
    margin-left: 2px;
  }
  */
  
  /* Update selectors to only target .form-group select */
  .form-group select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--gray-300);
    border-radius: 6px;
    font-size: 14px;
    line-height: 1.5;
    background-color: white;
    color: var(--gray-800);
    transition: border-color 0.2s, box-shadow 0.2s;
  }
  
  /* Update selectors to only target .form-group select:focus */
  .form-group select:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 2px var(--primary-100);
  }
  
  .form-group select {
    appearance: none; 
    background-image: url('data:image/svg+xml;utf8,<svg fill="%236B7280" height="20" viewBox="0 0 24 24" width="20" xmlns="http://www.w3.org/2000/svg"><path d="M7 10l5 5 5-5z"/><path d="M0 0h24v24H0z" fill="none"/></svg>'); 
    background-repeat: no-repeat;
    background-position: right 10px center;
    padding-right: 30px; 
  }
  
  .form-group select:disabled {
    background-color: var(--gray-100);
    cursor: not-allowed;
    opacity: 0.7;
  }
  
  
  /* Input with icon button */
  /*
  .input-with-button {
    position: relative;
    display: flex;
    align-items: center;
  }
  
  .input-with-button input {
    padding-right: 40px; 
    flex-grow: 1;
  }
  */
  
  .btn-icon-only {
    position: absolute;
    right: 1px; 
    top: 1px;
    bottom: 1px;
    width: 36px; 
    background: var(--gray-100);
    border: none;
    border-radius: 0 5px 5px 0; 
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--gray-600);
    transition: background-color 0.2s;
  }
  
  .btn-icon-only:hover {
    background: var(--gray-200);
  }
  .btn-icon-only svg {
    width: 20px;
    height: 20px;
  }
  
  
  /* Checkbox group */
  .checkbox-group {
    flex-direction: row;
    align-items: center;
    background-color: var(--blue-50); 
    padding: 12px 16px;
    border-radius: 6px;
    margin-bottom: 20px;
    gap: 8px; 
  }
  
  .checkbox-group input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: var(--primary-600);
    margin: 0; 
    cursor: pointer;
  }
  
  .checkbox-group label {
    margin-bottom: 0; 
    font-weight: 500;
    color: var(--gray-700);
    cursor: pointer;
  }
  
  .info-icon-small {
    color: var(--gray-400);
    width: 16px;
    height: 16px;
    cursor: help; 
  }
  
  /* Form Actions */
  /*
  .form-actions {
    display: flex;
    justify-content: flex-end; 
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid var(--gray-200);
  }
  */

  .stepsContainer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    margin-bottom: 30px; /* Increased margin for better spacing */
    padding: 0 10%; 
    width: 80%; /* Ensure it doesn't span full width if container is wider */
    margin-left: auto;
    margin-right: auto;
    margin-top: 20px;
  }

  .stepsLine {
    position: absolute;
    top: 15px; /* Aligns with the center of the stepCircle */
    left: 10%; 
    right: 10%; 
    height: 2px;
    background-color: #e0e0e0; 
    z-index: 1;
    width: calc(100% - 20% - 60px); /* Adjust width to not extend beyond circles */
    margin: 0 auto; /* Center the line itself if step circles are spaced out */
  }

  .step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2; 
    background-color: #fff; /* Give step a background to hide the line passing under the text */
    padding: 0 5px; /* Prevent text from touching line ends directly */
  }

  .stepCircle {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #fff; 
    border: 2px solid #e0e0e0; 
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    color: #ccc; 
    margin-bottom: 8px;
  }

  .stepCircleActive {
    background-color: #007bff; 
    border-color: #007bff; 
    color: #fff; 
  }

  .stepLabel {
    font-size: 14px;
    color: #333; 
    text-align: center;
    white-space: nowrap; /* Prevent label from wrapping */
  } 
  
  /* New Styles for PurchaseProgressModal */
  .modal-purchase-progress .modal-header {
    text-align: center;
    padding-top: 24px; /* More space at the top */
    padding-bottom: 12px;
    border-bottom: none; /* Remove border if not in new design */
  }

  .modal-purchase-progress .modal-header h3 {
    font-size: 22px; /* Larger title */
    font-weight: 600;
    color: var(--gray-900); /* Darker title */
    margin-bottom: 8px;
  }

  .modal-purchase-progress .modal-subtitle {
    font-size: 15px;
    color: var(--gray-600);
    margin-top: 0;
    margin-bottom: 24px; /* Space before loader */
  }

  .modal-purchase-progress .modal-content {
    padding: 16px 32px 32px 32px; /* Adjust padding */
  }

  .modal-purchase-progress .loader-container {
    margin-bottom: 28px; /* Space below loader */
    display: flex; /* Already flex, ensure centering */
    justify-content: center;
    align-items: center;
  }

  .custom-loader {
    width: 56px; /* Larger loader */
    height: 56px;
    border: 6px solid var(--primary-200); /* Lighter purple base */
    border-top-color: var(--primary-500); /* Darker purple for spinner part */
    border-radius: 50%;
    animation: rotation 0.8s linear infinite;
  }

  /* Re-using existing rotation animation if defined, or ensure it is: */
  /* @keyframes rotation { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } } */

  .progress-steps-alt {
    display: flex;
    flex-direction: column;
    gap: 20px; /* Space between steps */
  }

  .progress-step-alt {
    display: flex;
    align-items: center;
    gap: 12px; /* Space between icon and label */
  }

  .progress-step-icon-alt {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }

  /* Status-specific icon styling */
  .progress-step-alt.status-completed .progress-step-icon-alt {
    background-color: var(--green-100); /* Light green background */
  }
  .progress-step-alt.status-completed .progress-step-icon-alt::before {
    content: '\2713'; /* Unicode checkmark */
    color: var(--green-600); /* Dark green checkmark */
    font-size: 14px;
    font-weight: bold;
  }

  .progress-step-alt.status-in-progress .progress-step-icon-alt {
    background-color: var(--primary-500); /* Solid purple circle for in-progress */
    /* Optional: add a smaller inner dot or animation if desired */
    /* For a simple dot as per image, this is enough. For a spinner, it would be different */
     width: 12px; /* Smaller for a dot-like spinner */
     height: 12px;
     margin: 6px; /* Centering the smaller dot */
     /* If it should be an actual spinner, similar to .custom-loader but smaller: */
     /* border: 3px solid var(--primary-200); */
     /* border-top-color: var(--primary-500); */
     /* animation: rotation 0.7s linear infinite; */
     /* background-color: transparent; */
  }
  
  .progress-step-alt.status-pending .progress-step-icon-alt {
    background-color: var(--gray-200); /* Light gray background */
  }
  .progress-step-alt.status-pending .progress-step-icon-alt::before {
    content: '';
    display: block;
    width: 8px;
    height: 8px;
    background-color: var(--gray-400); /* Darker gray inner dot */
    border-radius: 50%;
  }
  .progress-step-label-alt {
    font-size: 15px;
    color: var(--gray-700);
  }

  .progress-step-alt.status-completed .progress-step-label-alt {
    color: var(--gray-500); /* Slightly de-emphasized */
  }

  .progress-step-alt.status-in-progress .progress-step-label-alt {
    color: var(--primary-600); /* Emphasized for current step */
    font-weight: 500;
  }

  /* Ensure modal container for this specific modal has appropriate width */
  .modal-purchase-progress {
    max-width: 420px; /* Narrower to match design */
  }
  