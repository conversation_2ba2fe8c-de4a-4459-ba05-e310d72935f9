import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import styles from '../style.module.css'; // Using the main style.module.css
import Button from '../../components/Button';
import { DOMAIN_PAGES } from '../RootContainer';

// Interfaces defined as per the plan
interface MailboxInfo {
  firstName: string;
  lastName: string;
  username: string;
}

export interface CompletedDomainSetup {
  domain: { name: string; id?: string };
  mailboxes: MailboxInfo[];
  initialSlotCount: number;
}

interface SetupCompleteState {
  completedMailboxConfigs?: CompletedDomainSetup[];
}

// --- SVG Icon Components ---
const SuccessCheckmarkIcon: React.FC = () => (
  <svg className={styles.successCheckmarkIcon} width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
    <circle cx="32" cy="32" r="32" fill="#E7F4E8"/> {/* Light green background */}
    <path d="M20 32.0001L28.0008 40.0009L44 24.0009" stroke="#34A853" strokeWidth="4" strokeLinecap="round" strokeLinejoin="round"/> {/* Green tick */}
  </svg>
);

const GlobeIcon: React.FC = () => (
  <svg className={styles.globeIcon} width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M10.0002 18.3334C14.6025 18.3334 18.3335 14.6024 18.3335 10C18.3335 5.39765 14.6025 1.66669 10.0002 1.66669C5.39781 1.66669 1.66683 5.39765 1.66683 10C1.66683 14.6024 5.39781 18.3334 10.0002 18.3334Z" stroke="#4A5568" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M1.66683 10H18.3335" stroke="#4A5568" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M10.0002 1.66669C12.0862 3.94531 13.2419 6.91051 13.3335 10C13.2419 13.0895 12.0862 16.0547 10.0002 18.3334C7.91415 16.0547 6.75841 13.0895 6.66683 10C6.75841 6.91051 7.91415 3.94531 10.0002 1.66669Z" stroke="#4A5568" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

const EnvelopeIcon: React.FC = () => (
  <svg className={styles.envelopeIcon} width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M3 3H15C15.825 3 16.5 3.675 16.5 4.5V13.5C16.5 14.325 15.825 15 15 15H3C2.175 15 1.5 14.325 1.5 13.5V4.5C1.5 3.675 2.175 3 3 3Z" stroke="#6366F1" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M16.5 4.5L9 9.75L1.5 4.5" stroke="#6366F1" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

const ArrowRightIcon: React.FC = () => (
  <svg className={styles.arrowIcon} width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M3.5 7H10.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M7.5 4L10.5 7L7.5 10" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);


interface SetupCompletePageProps {
  completedMailboxConfigs: CompletedDomainSetup[];
  setCurrentPage: (page: DOMAIN_PAGES) => void;
}

const SetupCompletePage: React.FC<SetupCompletePageProps> = ({ completedMailboxConfigs, setCurrentPage }) => {

  return (
    <div className={styles.setupCompletePageContainer}>
      <div className={styles.stepIndicatorContainer}>
        {/* Step 1: Domain - Completed */}
        <div className={styles.stepItem}>
          <div className={`${styles.stepCircle} ${styles.stepCircleCompleted}`}>
            <svg width="12" height="9" viewBox="0 0 12 9" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M10.7539 1.41699L4.43222 7.73866L1.24609 4.55254" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </div>
          <div className={`${styles.stepLabel} ${styles.stepLabelCompleted}`}>Domain</div>
        </div>

        <div className={`${styles.stepsLineBase} ${styles.progressState1}`}> {/* progressState1 means line is filled */}
          <div className={styles.stepsLineProgress}></div>
        </div>

        {/* Step 2: Mailboxes - Active */}
        <div className={styles.stepItem}>
          <div className={`${styles.stepCircle} ${styles.stepCircleActive}`}>
            <svg width="12" height="9" viewBox="0 0 12 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M10.7539 1.41699L4.43222 7.73866L1.24609 4.55254" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </div>
          <div className={`${styles.stepLabel} ${styles.stepLabelActive}`}>Mailboxes</div>
        </div>

        <div className={`${styles.stepsLineBase} ${styles.progressState1}`}> {/* progressState0 means line is not filled */}
            <div className={styles.stepsLineProgress}></div>
        </div>

        {/* Step 3: Complete - Upcoming */}
        <div className={styles.stepItem}>
          <div className={`${styles.stepCircle} ${styles.stepCircleActive}`}>
            <svg width="12" height="9" viewBox="0 0 12 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M10.7539 1.41699L4.43222 7.73866L1.24609 4.55254" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </div>
          <div className={`${styles.stepLabel} ${styles.stepLabelActive}`}>Complete</div>
        </div>
      </div>

      {/* New wrapper for main content */}
      <div className={styles.setupContentCard}>
        <div className={styles.successSection}>
          <SuccessCheckmarkIcon />
          <h1 className={styles.successTitle}>Setup Complete!</h1>
          <p className={styles.successSubtitle}>
            Your domains and mailboxes have been successfully set up. 
            You're now ready to start using them.
          </p>
        </div>

        <div className={styles.mailboxesGrid}>
          {completedMailboxConfigs.map((config) => (
            <div key={config.domain.id || config.domain.name} className={styles.domainCard}>
              <div className={styles.domainCardHeader}>
                <GlobeIcon />
                <span className={styles.domainName}>{config.domain.name}</span>
                <span className={styles.mailboxCreatedBadge}>
                  <svg width="12" height="12" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg" className={styles.badgeCheckmarkIcon}>
                      <path d="M11.6663 3.5L5.24967 9.91667L2.33301 7" stroke="#16A34A" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                  Mailbox Created ({config.mailboxes.length}/{config.initialSlotCount})
                </span>
              </div>
              <div className={styles.mailboxList}>
                {config.mailboxes.map((mb, mbIndex) => (
                  <div key={mbIndex} className={styles.mailboxItem}>
                    <EnvelopeIcon />
                    <div className={styles.mailboxInfo}>
                      <span className={styles.mailboxFullName}>{mb.firstName} {mb.lastName}</span>
                      <span className={styles.mailboxEmail}>{mb.username}@{config.domain.name}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        <div className={styles.addMoreSection}>
        <Button onClick={() => setCurrentPage(DOMAIN_PAGES.DOMAIN_PAGE)} variant="outline" className={styles.addMoreButton}>
            Go to homepage
            <ArrowRightIcon />
          </Button>
          <button onClick={() => setCurrentPage(DOMAIN_PAGES.BUY_DOMAINS)} className={styles.addMoreButton}>
            Add more Domain
            <ArrowRightIcon />
          </button>
        </div>
      </div> {/* End of setupContentCard */}
    </div>
  );
};

export default SetupCompletePage; 