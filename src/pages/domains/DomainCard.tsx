import React from 'react';
import { Domain } from './types';

interface DomainCardProps {
  domain: Domain;
  isSelected: boolean;
  isCartFull: boolean;
  onSelect: (domain: Domain) => void;
}

const DomainCard: React.FC<DomainCardProps> = ({ 
  domain, 
  isSelected, 
  isCartFull, 
  onSelect 
}) => {
  // Using separate elements with individual click handlers instead of event propagation

  // Handle domain selection directly
  const handleDomainSelect = () => {
    onSelect(domain);
  };

  return (
    <div className={`domain-card ${isSelected ? 'selected' : ''}`}>
      <div className="domain-info">
        {/* Clickable checkbox */}
        <button 
          className="checkbox-button" 
          onClick={handleDomainSelect}
          aria-checked={isSelected}
          role="checkbox"
          type="button"
        >
          <div className={`simple-checkbox ${isSelected ? 'checked' : ''}`}>
            {isSelected && (
              <svg viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M11.6666 3.5L5.24992 9.91667L2.33325 7" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            )}
          </div>
        </button>

        {/* Domain info - not clickable */}
        <div className="domain-text-info">
          <h4 className="domain-name">{domain.domainName}</h4>
          <span className="domain-status available">Available</span>
        </div>
      </div>

      {/* Add to cart button */}
      <div className="domain-actions">
        <button
          className="add-domain-btn"
          onClick={handleDomainSelect}
          disabled={isSelected || isCartFull}
          type="button"
        >
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <circle cx="9" cy="21" r="1"></circle>
            <circle cx="20" cy="21" r="1"></circle>
            <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
          </svg>
          Add Domain
        </button>
      </div>
    </div>
  );
};

export default DomainCard;