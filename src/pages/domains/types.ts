// Define domain interfaces
export interface Domain {
  domainName: string;
  available: boolean;
}

// Define context shape
interface DomainContextType {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  searchResults: Domain[];
  setSearchResults: (results: Domain[]) => void;
  selectedDomains: Domain[];
  addDomain: (domain: Domain) => void;
  removeDomain: (domainName: string) => void;
  isModalOpen: boolean;
  setIsModalOpen: (isOpen: boolean) => void;
  filteredResults: Domain[];
  domainCount: number;
  clearResults: () => void;
  searchHistory: string[];
  addToSearchHistory: (term: string) => void;
  clearSearchHistory: () => void;
  suggestedDomains: Domain[];
  setSuggestedDomains: (domains: Domain[]) => void;
}
