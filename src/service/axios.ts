import { AxiosError, AxiosRequestConfig, default as Axios, InternalAxiosRequestConfig } from 'axios';

import { v4 } from 'uuid';
import { ERROR_MESSAGES, getSessionStorageItem } from '@klenty/klenty-ui';

const authInstance = Axios.create({
  baseURL:"http://localhost:9095/",
  withCredentials: true,
});

declare module 'axios' {
  export interface AxiosRequestConfig {
    trackForChatBot?: boolean;
  }
}

const appInstance = Axios.create();


const interceptorFulfilled = (config:InternalAxiosRequestConfig) => {
  const workspaceId = getSessionStorageItem('kl-sm-wid');
  config.headers['x-kl-sm-wid'] = workspaceId || '';
  config.headers['x-kl-sm-time'] = new Date().getTime();
  config.headers['x-kl-sm-rid'] = v4();
  config.headers['x-kl-sm-uid'] = '67da5ea11b7697720d7b959f';
  
//   if (process.env.NODE_ENV === 'development') {
//     const userIdToken = getSessionStorageItem('kl-dev-userId');
//     config.headers['x-kl-sm-uid'] = userIdToken || '';
//   }

  return config;
};

const interceptorRejected = (error: AxiosError) => {
    return Promise.reject(error);
};

appInstance.interceptors.request.use(interceptorFulfilled, interceptorRejected);
authInstance.interceptors.request.use(interceptorFulfilled,interceptorRejected);
  
appInstance.interceptors.response.use(undefined, interceptorRejected);

authInstance.interceptors.response.use(undefined, interceptorRejected);

export interface KlentyResponse<T> {
  status: boolean;
  data: T;
  errors: Array<{
    errorCode: string;
    domain: string;
    errorMessage: string;
    type: string;
    block: string;
  }>;
}

export interface ExtendedAxiosRequestConfig extends AxiosRequestConfig {
  isReadEndpoint?: boolean;
  isNewWebApp?: boolean;
}

export const handleResponseErrors = (err: any) => {
  if (Axios.isCancel(err)) return Promise.reject(ERROR_MESSAGES.KL600);
  else if (err.response && err.response.status === 404) {
    return Promise.reject(ERROR_MESSAGES.KL404);
  } else if (err.response && err.response.status === 403) {
    return Promise.reject(ERROR_MESSAGES.KL403);
  } else if (err.response && err.response.status === 401) {
    if (process.env.NODE_ENV !== 'development') {
      return Promise.reject((window.location.href = '/'));
    }
    return Promise.reject(ERROR_MESSAGES.KL401);
  } else if (err.response && err.response.status === 500) {
    return Promise.reject(ERROR_MESSAGES.KL500);
  }

  return Promise.reject(ERROR_MESSAGES.KL2000);
};

const axios = {
  auth: {
    get: <T, V = KlentyResponse<T>>(
      uri: string,
      config?: ExtendedAxiosRequestConfig
    ): Promise<V> => {
      if (config?.isReadEndpoint) uri = '/READ' + uri;
      // if (config?.isNewWebApp) uri = '/NEW' + uri;
      return authInstance
        .get<V>(uri, config)
        .then((result) => result.data)
        .catch((err) => handleResponseErrors(err));
    },
    post: <T, U = unknown, V = KlentyResponse<T>>(
      uri: string,
      data: U,
      config?: ExtendedAxiosRequestConfig
    ): Promise<V> => {
      if (config?.isReadEndpoint) uri = '/READ' + uri;
      // if (config?.isNewWebApp) uri = '/NEW' + uri;
      return authInstance
        .post<V>(uri, data, config)
        .then((result) => result.data)
        .catch((err) => handleResponseErrors(err));
    },
    put: <T, U = unknown, V = KlentyResponse<T>>(
      uri: string,
      data: U,
      config?: ExtendedAxiosRequestConfig
    ): Promise<V> => {
      if (config?.isReadEndpoint) uri = '/READ' + uri;
      // if (config?.isNewWebApp) uri = '/NEW' + uri;
      return authInstance
        .put<V>(uri, data, config)
        .then((result) => result.data)
        .catch((err) => handleResponseErrors(err));
    },
    patch: <T, U = unknown, V = KlentyResponse<T>>(
      uri: string,
      data: U,
      config?: ExtendedAxiosRequestConfig
    ): Promise<V> => {
      if (config?.isReadEndpoint) uri = '/READ' + uri;
      // if (config?.isNewWebApp) uri = '/NEW' + uri;
      return authInstance
        .patch<V>(uri, data, config)
        .then((result) => result.data)
        .catch((err) => handleResponseErrors(err));
    },
    delete: <T, U = unknown, V = KlentyResponse<T>>(
      uri: string,
      config?: ExtendedAxiosRequestConfig
    ): Promise<V> => {
      if (config?.isReadEndpoint) uri = '/READ' + uri;
      // if (config?.isNewWebApp) uri = '/NEW' + uri;
      return authInstance
        .delete<V>(uri, config)
        .then((result) => result.data)
        .catch((err) => handleResponseErrors(err));
    },
  },
  get: <T, V = KlentyResponse<T>>(
    uri: string,
    config?: ExtendedAxiosRequestConfig
  ): Promise<V> => {
    if (config?.isReadEndpoint) uri = '/READ' + uri;
    // if (config?.isNewWebApp) uri = '/NEW' + uri;
    return appInstance
      .get<V>(uri, config)
      .then((result) => result.data)
      .catch((err) => handleResponseErrors(err));
  },
  post: <T, U = unknown, V = KlentyResponse<T>>(
    uri: string,
    data: U,
    config?: ExtendedAxiosRequestConfig
  ): Promise<V> => {
    if (config?.isReadEndpoint) uri = '/READ' + uri;
    // if (config?.isNewWebApp) uri = '/NEW' + uri;
    return appInstance
      .post<V>(uri, data, config)
      .then((result) => result.data)
      .catch((err) => handleResponseErrors(err));
  },
  put: <T, U = unknown, V = KlentyResponse<T>>(
    uri: string,
    data: U,
    config?: ExtendedAxiosRequestConfig
  ): Promise<V> => {
    if (config?.isReadEndpoint) uri = '/READ' + uri;
    // if (config?.isNewWebApp) uri = '/NEW' + uri;
    return appInstance
      .put<V>(uri, data, config)
      .then((result) => result.data)
      .catch((err) => handleResponseErrors(err));
  },
  patch: <T, U = unknown, V = KlentyResponse<T>>(
    uri: string,
    data: U,
    config?: ExtendedAxiosRequestConfig
  ): Promise<V> => {
    if (config?.isReadEndpoint) uri = '/READ' + uri;
    // if (config?.isNewWebApp) uri = '/NEW' + uri;
    return appInstance
      .patch<V>(uri, data, config)
      .then((result) => result.data)
      .catch((err) => handleResponseErrors(err));
  },
  delete: <T, U = unknown, V = KlentyResponse<T>>(
    uri: string,
    data: U,
    config?: ExtendedAxiosRequestConfig
  ): Promise<V> => {
    if (config?.isReadEndpoint) uri = '/READ' + uri;
    // if (config?.isNewWebApp) uri = '/NEW' + uri;
    return appInstance
      .delete<V>(uri, config)
      .then((result) => result.data)
      .catch((err) => handleResponseErrors(err));
  },
  //Meant for external API calls
  external: {
    put: appInstance.put,
  },
};

export default axios;
