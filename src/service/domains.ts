import { Domain } from '@/pages/domains/types';
import axios from './axios';

export interface SearchDomainsParams {
  query?: string;
}

export interface SearchDomainsResponse {
  domainName: string;
}

export interface CreationMailboxConfig {
  username: string;
  firstName: string;
  lastName: string;
  errors?: { firstName?: string; lastName?: string; username?: string }; // frontend only
}

export interface SetupMailBoxForDomainsParams {
  mailboxes: {
    domain:string;
    config:CreationMailboxConfig[];
  }[]
}


export interface SetupMailBoxForDomainsResponse {
  success: boolean;
  data: any;
  message: string;
}

export enum MAILBOX_STATUS {
  PENDING = 0,
  ZAP_CONNECTED = 1,
  KLENTY_CONNECTED = 2,
  ERROR = 3,
}



export enum DOMAIN_STATUS {
  PENDING = 0,
  CONNECTED = 1,
  ERROR = 2,

}
export const mailboxStatusToString = (status: MAILBOX_STATUS) => {
  switch (status) {
    case MAILBOX_STATUS.PENDING:
      return "Pending";
    case MAILBOX_STATUS.ZAP_CONNECTED:
      return "Zap Connected";
    case MAILBOX_STATUS.KLENTY_CONNECTED:
      return "Klenty Connected";
    case MAILBOX_STATUS.ERROR:
      return "Error";
    default:
      return "Unknown";
  }
};

export const domainStatusToString = (status: DOMAIN_STATUS) => {
  switch (status) {
    case DOMAIN_STATUS.PENDING:
      return "Pending";
    case DOMAIN_STATUS.CONNECTED:
      return "Connected";
    case DOMAIN_STATUS.ERROR:
      return "Error";
    default:
      return "Unknown";
  }
};
export interface MailBoxConfig {
  id?: string;
  username: string;
  assignedUser?: string;
  firstName: string;
  lastName: string;
  status: MAILBOX_STATUS;
  errorMsg?: string;
}

export interface MailBoxes {
  domain: string;
  status: DOMAIN_STATUS;
  config: MailBoxConfig[];
  errorMsg?: string;
  date: string;
}

export async function searchDomains(params: SearchDomainsParams): Promise<Array<Domain>> {
    return axios
    .get<Array<Domain>>('/api/mailbox/domain/search?domain=' + params.query,{isNewWebApp:true})
    .then(function (result) {
      if (result.status) return result.data;
      else return Promise.reject(result.errors[0].errorMessage);
    });
}

export async function setupMailBoxForDomains(body: SetupMailBoxForDomainsParams): Promise<SetupMailBoxForDomainsResponse> {
  return axios
    .post<SetupMailBoxForDomainsResponse>('/api/mailbox/setup', body,{isNewWebApp:true})
    .then(function (result) {
      if (result.status) return result.data;
      else return Promise.reject(result.errors[0].errorMessage);
    });
}

export async function fetchAllMailboxes(): Promise<MailBoxes[]> {
  return axios
    .get<MailBoxes[]>('/api/mailbox/all', { isNewWebApp: true })
    .then(function (result) {
      if (result.status) return result.data;
      else return Promise.reject(result.errors?.[0]?.errorMessage || 'Unknown error');
    });
}   