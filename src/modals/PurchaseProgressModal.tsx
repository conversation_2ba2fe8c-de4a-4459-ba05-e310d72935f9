import React, { useEffect, useState } from 'react';
import { Domain } from '../pages/domains/types';
import styles from '../style.module.css'; // Import CSS Modules
// import { setupMailBoxForDomains } from '../../service/domains'; // Mocking API call

interface ProgressStep {
  id: number;
  label: string;
  status: 'completed' | 'in-progress' | 'pending';
  // progress: number; // Progress percentage not shown in new design, status icon is enough
}

interface PurchaseProgressModalProps {
  setIsModalOpen: (isOpen: boolean) => void;
  selectedDomains: Domain[];
  onPurchaseComplete?: () => void;
}

const PurchaseProgressModal = ({ setIsModalOpen, selectedDomains, onPurchaseComplete }: PurchaseProgressModalProps) => {
  const [currentDomainName, setCurrentDomainName] = useState<string>('');

  const initialSteps: ProgressStep[] = [
    { id: 1, label: 'Verifying domain availability', status: 'in-progress' }, // Correctly starts in-progress
    { id: 2, label: 'Preparing domain registration', status: 'pending' },
    { id: 3, label: 'Setting up DNS records', status: 'pending' },
    { id: 4, label: 'Configuring email services', status: 'pending' },
    { id: 5, label: 'Finalizing domain setup', status: 'pending' },
  ];
  const [progressSteps, setProgressSteps] = useState<ProgressStep[]>(initialSteps);

  useEffect(() => {
    if (selectedDomains.length > 0) {
      setCurrentDomainName(selectedDomains[0].domainName);
    } else {
      // Fallback or handle case where no domain is selected, though modal might not open then
      setCurrentDomainName('your domain'); 
    }
  }, [selectedDomains]);

  useEffect(() => {
    const timers: NodeJS.Timeout[] = [];
    let currentProcessingStepId = 1; // Start with the first step

    const advanceStep = () => {
      if (currentProcessingStepId > 5) {
        // All steps done
        const finalTimer = setTimeout(() => {

          onPurchaseComplete?.();
          setIsModalOpen(false);
        }, 1000);
        timers.push(finalTimer);
        return;
      }

      // Mark current step as 'in-progress' (it might already be if it's the first step from initial state)
      // Then, after a delay, mark it as 'completed' and trigger the next step.
      setProgressSteps(prevSteps =>
        prevSteps.map(step =>
          step.id === currentProcessingStepId ? { ...step, status: 'in-progress' } : step
        )
      );

      const stepCompletionTimer = setTimeout(() => {
        setProgressSteps(prevSteps =>
          prevSteps.map(step =>
            step.id === currentProcessingStepId ? { ...step, status: 'completed' } : step
          )
        );
        currentProcessingStepId++;
        advanceStep(); // Move to the next step
      }, 2000); // 2-second processing time for each step

      timers.push(stepCompletionTimer);
    };

    advanceStep(); // Start the process

    return () => {
      timers.forEach(clearTimeout);
    };
  }, [setIsModalOpen, onPurchaseComplete]); // Dependencies

  return (
    <div className={styles.modalOverlay}>
      <div className={`${styles.modalContainer} ${styles.modalPurchaseProgress}`}> {/* Added specific class for easier styling */}
        <div className={styles.modalHeader}>
          <h3>Setting up your domain</h3>
          <p className={styles.modalSubtitle}>Processing your domain purchases... this may take a few minutes.</p>
        </div>
        <div className={styles.modalContent}>
          <div className={styles.loaderContainer}>
            {/* Loader will be styled by CSS to be the purple spinner */}
            <div className={styles.customLoader}></div> 
          </div>
          <div className={styles.progressStepsAlt}> {/* Using a new class for new step design */}
            {progressSteps.map(step => (
              <div key={step.id} className={`${styles.progressStepAlt} ${step.status === 'completed' ? styles.statusCompleted : step.status === 'in-progress' ? styles.statusInProgress : styles.statusPending}`}>
                <div className={styles.progressStepIconAlt}>
                  {/* Icons will be rendered via CSS based on status */}
                </div>
                <span className={styles.progressStepLabelAlt}>{step.label}</span>
              </div>
            ))}
          </div>
        </div>
        {/* Footer with Cancel button removed */}
      </div>
    </div>
  );
};

export default PurchaseProgressModal;
