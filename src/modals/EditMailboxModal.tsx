import React, { useEffect, useState } from 'react';
import styles from '../pages/style.module.css'; 
import { DomainDetails } from '@/pages/RootContainer';

// Basic SVG for Close Icon - Consider moving to a shared icons file if used elsewhere
const CloseIcon = () => (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" width="24" height="24">
      <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
    </svg>
);

interface EditMailboxModalProps {
  isOpen: boolean;
  onClose: () => void;
  mailbox: DomainDetails["mailboxes"][0];
  onSubmit: (updatedMailbox: DomainDetails["mailboxes"][0]) => void;
}

const EditMailboxModal: React.FC<EditMailboxModalProps> = ({ isOpen, onClose, mailbox, onSubmit }) => {
    const [editableFirstName, setEditableFirstName] = useState('');
    const [editableLastName, setEditableLastName] = useState('');
    const [editableEmailPrefix, setEditableEmailPrefix] = useState('');

    useEffect(() => {
        if (mailbox) {
            // Populate first and last name from mailbox.username
            const nameParts = mailbox.username.split(' ');
            setEditableFirstName(nameParts[0] || '');
            setEditableLastName(nameParts.slice(1).join(' ') || '');
            
            // Populate email prefix from mailbox.emailAddress
            if (mailbox.emailAddress) {
                setEditableEmailPrefix(mailbox.emailAddress.split('@')[0] || '');
            }
        }
    }, [mailbox]);

    if (!isOpen || !mailbox) { // Also check if mailbox is null/undefined
        return null;
    }

    const handleFormSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        const updatedUsername = `${editableFirstName} ${editableLastName}`.trim();
        const updatedEmailAddress = `${editableEmailPrefix}@${mailbox.domainName}`;

        const updatedMailboxData:  DomainDetails["mailboxes"][0] = {
            ...mailbox, 
            username: updatedUsername,
            emailAddress: updatedEmailAddress,
            mailboxName: updatedEmailAddress,
        };
        onSubmit(updatedMailboxData);
        
    };

    return (
        <div className={styles.modalOverlay} onClick={onClose}>
            <div className={styles.modalContainer} onClick={(e) => e.stopPropagation()}>
                <form onSubmit={handleFormSubmit}>
                    <div className={styles.modalHeader}>
                        <h2 className={styles.modalTitle}>Edit Mailbox for {mailbox.emailAddress}</h2>
                        <button type="button" className={styles.closeButton} onClick={onClose}>
                            <CloseIcon />
                        </button>
                    </div>
                    <div className={styles.modalBody}>
                        <div className={styles.formGroup}>
                            <label htmlFor="firstName" className={styles.formLabel}>First Name</label>
                            <input 
                                type="text" 
                                id="firstName" 
                                className={styles.formInput}
                                value={editableFirstName}
                                onChange={(e) => setEditableFirstName(e.target.value)}
                            />
                        </div>
                        <div className={styles.formGroup}>
                            <label htmlFor="lastName" className={styles.formLabel}>Last Name</label>
                            <input 
                                type="text" 
                                id="lastName" 
                                className={styles.formInput}
                                value={editableLastName}
                                onChange={(e) => setEditableLastName(e.target.value)}
                            />
                        </div>
                        {/* <div className={styles.formGroup}>
                            <label htmlFor="emailPrefix" className={styles.formLabel}>Username</label>
                            <div className={styles.inputWithSuffixContainer}> 
                                <input 
                                    type="text" 
                                    id="emailPrefix" 
                                    className={styles.formInput} // May need a specific style if width needs to be adjusted
                                    value={editableEmailPrefix}
                                    onChange={(e) => setEditableEmailPrefix(e.target.value)}
                                />
                                <span className={styles.inputSuffix}>@{mailbox.domainName}</span>
                            </div>
                        </div> */}
                    </div>
                    <div className={styles.modalFooter}>
                        <button type="button" className={styles.cancelButton} onClick={onClose}>
                            Cancel
                        </button>
                        <button type="submit" className={styles.submitButton}>
                            Save Changes
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default EditMailboxModal;