import React, { useEffect, useRef, useState } from 'react';
import styles from '../pages/style.module.css';

// Basic SVG for Close Icon
const CloseIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" width="24" height="24">
    <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
  </svg>
);

// SVG for Chevron Down Icon
const ChevronDownIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" width="20" height="20">
    <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
  </svg>
);

// SVG for Pill Remove Icon
const PillRemoveIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" width="16" height="16">
    <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
  </svg>
);

export interface Domain {
  id: string;
  name: string;
}

interface CreateMailboxModalProps {
  isOpen: boolean;
  onClose: () => void;
  availableDomains: Domain[];
  onSubmit: (selectedDomainIds: string[]) => void;
}

const CreateMailboxModal: React.FC<CreateMailboxModalProps> = ({
  isOpen,
  onClose,
  availableDomains,
  onSubmit,
}) => {
  const [selectedDomainIds, setSelectedDomainIds] = useState<string[]>([]);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null); // For detecting outside clicks

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };
    if (isOpen && isDropdownOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, isDropdownOpen]);


  if (!isOpen) {
    return null;
  }

  const handleToggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  const handleDomainSelection = (domainId: string) => {
    setSelectedDomainIds(prevSelected => 
      prevSelected.includes(domainId)
        ? prevSelected.filter(id => id !== domainId)
        : [...prevSelected, domainId]
    );
  };

  const handleRemovePill = (domainId: string, event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent dropdown from toggling
    setSelectedDomainIds(prevSelected => prevSelected.filter(id => id !== domainId));
  };

  const handleSubmit = () => {
    onSubmit(selectedDomainIds);
  };
  
  const getDomainNameById = (id: string) => {
    return availableDomains.find(d => d.id === id)?.name || '';
  }

  return (
    <div className={styles.modalOverlay} onClick={onClose}>
      <div className={styles.modalContainer} onClick={(e) => e.stopPropagation()}>
        <div className={styles.modalHeader}>
          <h2 className={styles.modalTitle}>Create Mailbox</h2>
          <button className={styles.closeButton} onClick={onClose}>
            <CloseIcon />
          </button>
        </div>
        <div className={styles.modalBody}>
          <p className={styles.modalSubtitle}>
            Select one or more domains to create mailboxes
          </p>
          
          <div className={styles.customDropdown} ref={dropdownRef}>
            <div className={styles.dropdownInputArea} onClick={handleToggleDropdown}>
              {selectedDomainIds.length === 0 && (
                <span className={styles.dropdownPlaceholderText}>Select domain(s)</span>
              )}
              {selectedDomainIds.map(id => (
                <div key={id} className={styles.domainPill}>
                  {getDomainNameById(id)}
                  <button className={styles.pillRemoveIcon} onClick={(e) => handleRemovePill(id, e)}>
                    <PillRemoveIcon />
                  </button>
                </div>
              ))}
              <span className={styles.dropdownArrow}>
                <ChevronDownIcon />
              </span>
            </div>
            {isDropdownOpen && (
              <div className={styles.dropdownOptionsList}>
                {availableDomains.map(domain => (
                  <label key={domain.id} className={`${styles.dropdownOption} ${selectedDomainIds.includes(domain.id) ? styles.selectedOption : ''}`}>
                    <input 
                      type="checkbox" 
                      checked={selectedDomainIds.includes(domain.id)}
                      onChange={() => handleDomainSelection(domain.id)}
                      className={styles.optionCheckbox}
                    />
                    {domain.name}
                  </label>
                ))}
              </div>
            )}
          </div>

        </div>
        <div className={styles.modalFooter}>
          <button className={styles.cancelButton} onClick={onClose}>
            Cancel
          </button>
          <button 
            className={styles.submitButton} 
            onClick={handleSubmit} 
            disabled={selectedDomainIds.length === 0} // Disable if no domains selected
          >
            Create Mailboxes
          </button>
        </div>
      </div>
    </div>
  );
};

export default CreateMailboxModal; 