import React, { Dispatch, SetStateAction, useEffect, useRef } from "react";
import { createRoot, Root } from "react-dom/client";
import {
  BrowserRouter,
  MemoryRouter,
  Route,
  Routes,
  useLocation,
  useNavigate,
} from "react-router-dom";
import styles from "./App.module.css";
import DomainsPage from "./pages/domains";
import RootContainer from "./pages/RootContainer";

import "./global.css"

const AppRouter = ({
  isMicrofrontEnd,
  eventName,
  onNavigate,
  setInternalPath,
  children,
}: {
  isMicrofrontEnd: boolean;
  eventName: string;
  onNavigate: (path: string) => void;
  setInternalPath: Dispatch<SetStateAction<string>>;
  children: React.ReactNode;
}) => {
  if (isMicrofrontEnd) {
    return (
      <MemoryRouter initialEntries={["/domains"]}>
        <RouteChangeListener
          eventName={eventName}
          onPathChange={onNavigate}
          setInternalPath={setInternalPath}
        />
        {children}
      </MemoryRouter>
    );
  }
  return (
    <BrowserRouter>
      {children}
    </BrowserRouter>
  );
};

const mount = (
  element: HTMLElement,
  onNavigate: (path: string) => void,
  initialPath: string,
  eventName: string,
  isMicrofrontEnd: boolean
) => {
  console.log("mounting", element, isMicrofrontEnd);

  const App = () => {
    const [internalPath, setInternalPath] = React.useState(initialPath);

    const appRoutes = (
      <Routes>
        <Route path="/domains" element={<RootContainer />} />
        <Route path="/" element={<RootContainer />} />
      </Routes>
    );

    return (
      <div className={styles.app}>
        <AppRouter
          isMicrofrontEnd={isMicrofrontEnd}
          eventName={eventName}
          onNavigate={onNavigate}
          setInternalPath={setInternalPath}
        >
          {appRoutes}
        </AppRouter>
      </div>
    );
  };

  const root: Root = createRoot(element);
  root.render(
    <React.StrictMode>
      <App />
    </React.StrictMode>
  );

  return {
    unmount: () => {
      root.unmount();
    },
  };
};

const RouteChangeListener = ({
  onPathChange,
  setInternalPath,
  eventName,
}: {
  onPathChange: (path: string) => void;
  setInternalPath: Dispatch<SetStateAction<string>>;
  eventName: string;
}) => {
  const location = useLocation();
  const currentLocation = useRef(location.pathname);
  const navigate = useNavigate();
  const lastPathRef = useRef(location.pathname);

  useEffect(() => {
    if (lastPathRef.current !== location.pathname) {
      lastPathRef.current = location.pathname;
      currentLocation.current = location.pathname;
      onPathChange(location.pathname);
      setInternalPath(location.pathname);
    }
  }, [location.pathname]);

  useEffect(() => {
    const handleContainerNavigate = (e: Event) => {
      const path = (e as CustomEvent).detail;
      setInternalPath(path);
      if (currentLocation.current !== path) {
        currentLocation.current = path;
        lastPathRef.current = location.pathname;
        navigate(path);
      }
    };
    window.addEventListener(eventName, handleContainerNavigate);
    return () => {
      window.removeEventListener(eventName, handleContainerNavigate);
    };
  }, []);

  return null;
};

const devRootElement = document.getElementById('root');
if (devRootElement) {
  mount(devRootElement, () => {}, "", "", true);
}

export default mount;
