{"logging": {}, "time": 24807, "errors": [{"message": "  \u001b[31m×\u001b[0m Error: \u001b[90mTS2339: \u001b[39mProperty 'name' does not exist on type 'Domain'.\n  \u001b[31m│\u001b[0m   \u001b[0m \u001b[90m 20 |\u001b[39m           \u001b[33m<\u001b[39m\u001b[33mdiv\u001b[39m key\u001b[33m=\u001b[39m{index} className\u001b[33m=\u001b[39m\u001b[32m\"suggestion-card\"\u001b[39m\u001b[33m>\u001b[39m\n  \u001b[31m│\u001b[0m    \u001b[90m 21 |\u001b[39m             \u001b[33m<\u001b[39m\u001b[33mdiv\u001b[39m className\u001b[33m=\u001b[39m\u001b[32m\"suggestion-header\"\u001b[39m\u001b[33m>\u001b[39m\n  \u001b[31m│\u001b[0m   \u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 22 |\u001b[39m               \u001b[33m<\u001b[39m\u001b[33mh4\u001b[39m\u001b[33m>\u001b[39m{domain\u001b[33m.\u001b[39mname}\u001b[33m<\u001b[39m\u001b[33m/\u001b[39m\u001b[33mh4\u001b[39m\u001b[33m>\u001b[39m\n  \u001b[31m│\u001b[0m    \u001b[90m    |\u001b[39m                           \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\u001b[31m\u001b[1m^\u001b[22m\u001b[39m\u001b[31m\u001b[1m^\u001b[22m\u001b[39m\u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n  \u001b[31m│\u001b[0m    \u001b[90m 23 |\u001b[39m               {\u001b[90m/* <span className={`availability-tag ${domain.available ? 'available' : 'unavailable'}`}>\u001b[39m\n  \u001b[31m│\u001b[0m    \u001b[90m 24 |\u001b[39m \u001b[90m                {domain.available ? 'Available' : 'Unavailable'}\u001b[39m\n  \u001b[31m│\u001b[0m    \u001b[90m 25 |\u001b[39m \u001b[90m              </span> */\u001b[39m}\u001b[0m\n\u001b[0m", "file": "./src/pages/domains/DomainSuggestions.tsx:22:27"}, {"message": "  \u001b[31m×\u001b[0m Error: \u001b[90mTS2339: \u001b[39mProperty 'premium' does not exist on type 'Domain'.\n  \u001b[31m│\u001b[0m   \u001b[0m \u001b[90m 26 |\u001b[39m             \u001b[33m<\u001b[39m\u001b[33m/\u001b[39m\u001b[33mdiv\u001b[39m\u001b[33m>\u001b[39m\n  \u001b[31m│\u001b[0m    \u001b[90m 27 |\u001b[39m             \u001b[33m<\u001b[39m\u001b[33mdiv\u001b[39m className\u001b[33m=\u001b[39m\u001b[32m\"suggestion-footer\"\u001b[39m\u001b[33m>\u001b[39m\n  \u001b[31m│\u001b[0m   \u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 28 |\u001b[39m               \u001b[33m<\u001b[39m\u001b[33mp\u001b[39m\u001b[33m>\u001b[39m{domain\u001b[33m.\u001b[39mpremium \u001b[33m?\u001b[39m \u001b[32m'Premium domain'\u001b[39m \u001b[33m:\u001b[39m \u001b[32m'Standard domain'\u001b[39m}\u001b[33m<\u001b[39m\u001b[33m/\u001b[39m\u001b[33mp\u001b[39m\u001b[33m>\u001b[39m\n  \u001b[31m│\u001b[0m    \u001b[90m    |\u001b[39m                          \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\u001b[31m\u001b[1m^\u001b[22m\u001b[39m\u001b[31m\u001b[1m^\u001b[22m\u001b[39m\u001b[31m\u001b[1m^\u001b[22m\u001b[39m\u001b[31m\u001b[1m^\u001b[22m\u001b[39m\u001b[31m\u001b[1m^\u001b[22m\u001b[39m\u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n  \u001b[31m│\u001b[0m    \u001b[90m 29 |\u001b[39m               \u001b[33m<\u001b[39m\u001b[33mbutton\u001b[39m\n  \u001b[31m│\u001b[0m    \u001b[90m 30 |\u001b[39m                   className\u001b[33m=\u001b[39m\u001b[32m\"add-domain-button\"\u001b[39m\n  \u001b[31m│\u001b[0m    \u001b[90m 31 |\u001b[39m                   disabled\u001b[33m=\u001b[39m{selectedDomains\u001b[33m.\u001b[39msome(d \u001b[33m=>\u001b[39m d\u001b[33m.\u001b[39mname \u001b[33m===\u001b[39m domain\u001b[33m.\u001b[39mname) \u001b[33m||\u001b[39m selectedDomains\u001b[33m.\u001b[39mlength \u001b[33m>=\u001b[39m \u001b[35m2\u001b[39m}\u001b[0m\n\u001b[0m", "file": "./src/pages/domains/DomainSuggestions.tsx:28:26"}, {"message": "  \u001b[31m×\u001b[0m Error: \u001b[90mTS2339: \u001b[39mProperty 'name' does not exist on type 'Domain'.\n  \u001b[31m│\u001b[0m   \u001b[0m \u001b[90m 29 |\u001b[39m               \u001b[33m<\u001b[39m\u001b[33mbutton\u001b[39m\n  \u001b[31m│\u001b[0m    \u001b[90m 30 |\u001b[39m                   className\u001b[33m=\u001b[39m\u001b[32m\"add-domain-button\"\u001b[39m\n  \u001b[31m│\u001b[0m   \u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m                   disabled\u001b[33m=\u001b[39m{selectedDomains\u001b[33m.\u001b[39msome(d \u001b[33m=>\u001b[39m d\u001b[33m.\u001b[39mname \u001b[33m===\u001b[39m domain\u001b[33m.\u001b[39mname) \u001b[33m||\u001b[39m selectedDomains\u001b[33m.\u001b[39mlength \u001b[33m>=\u001b[39m \u001b[35m2\u001b[39m}\n  \u001b[31m│\u001b[0m    \u001b[90m    |\u001b[39m                                                         \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\u001b[31m\u001b[1m^\u001b[22m\u001b[39m\u001b[31m\u001b[1m^\u001b[22m\u001b[39m\u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n  \u001b[31m│\u001b[0m    \u001b[90m 32 |\u001b[39m                   onClick\u001b[33m=\u001b[39m{() \u001b[33m=>\u001b[39m addDomain(domain)}\n  \u001b[31m│\u001b[0m    \u001b[90m 33 |\u001b[39m                 \u001b[33m>\u001b[39m\n  \u001b[31m│\u001b[0m    \u001b[90m 34 |\u001b[39m                   \u001b[33mAdd\u001b[39m to \u001b[33mCart\u001b[39m\u001b[0m\n\u001b[0m", "file": "./src/pages/domains/DomainSuggestions.tsx:31:57"}, {"message": "  \u001b[31m×\u001b[0m Error: \u001b[90mTS2339: \u001b[39mProperty 'name' does not exist on type 'Domain'.\n  \u001b[31m│\u001b[0m   \u001b[0m \u001b[90m 29 |\u001b[39m               \u001b[33m<\u001b[39m\u001b[33mbutton\u001b[39m\n  \u001b[31m│\u001b[0m    \u001b[90m 30 |\u001b[39m                   className\u001b[33m=\u001b[39m\u001b[32m\"add-domain-button\"\u001b[39m\n  \u001b[31m│\u001b[0m   \u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 31 |\u001b[39m                   disabled\u001b[33m=\u001b[39m{selectedDomains\u001b[33m.\u001b[39msome(d \u001b[33m=>\u001b[39m d\u001b[33m.\u001b[39mname \u001b[33m===\u001b[39m domain\u001b[33m.\u001b[39mname) \u001b[33m||\u001b[39m selectedDomains\u001b[33m.\u001b[39mlength \u001b[33m>=\u001b[39m \u001b[35m2\u001b[39m}\n  \u001b[31m│\u001b[0m    \u001b[90m    |\u001b[39m                                                                         \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\u001b[31m\u001b[1m^\u001b[22m\u001b[39m\u001b[31m\u001b[1m^\u001b[22m\u001b[39m\u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n  \u001b[31m│\u001b[0m    \u001b[90m 32 |\u001b[39m                   onClick\u001b[33m=\u001b[39m{() \u001b[33m=>\u001b[39m addDomain(domain)}\n  \u001b[31m│\u001b[0m    \u001b[90m 33 |\u001b[39m                 \u001b[33m>\u001b[39m\n  \u001b[31m│\u001b[0m    \u001b[90m 34 |\u001b[39m                   \u001b[33mAdd\u001b[39m to \u001b[33mCart\u001b[39m\u001b[0m\n\u001b[0m", "file": "./src/pages/domains/DomainSuggestions.tsx:31:73"}], "errorsCount": 8, "warnings": [{"message": "  \u001b[33m⚠\u001b[0m asset size limit: The following asset(s) exceed the recommended size limit (500.000 KiB). This can impact web performance.\n  \u001b[33m│\u001b[0m Assets:\n  \u001b[33m│\u001b[0m   396.8ff4e7881c52c95b.js (2.743 MiB)\n\u001b[0m"}], "warningsCount": 2}