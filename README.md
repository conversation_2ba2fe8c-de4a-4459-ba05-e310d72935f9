# Mailbox UI Microfrontend

A microfrontend application built with React, rspack, TypeScript, CSS Modules, and Biome linting.

## Features

- React application configured as a microfrontend
- TypeScript for type safety
- rspack for efficient bundling
- CSS Modules for component-scoped styling
- Biome for linting and formatting
- Module Federation for microfrontend architecture

## Getting Started

### Prerequisites

- Node.js (v16 or later)
- npm or yarn

### Installation

```bash
# Install dependencies
npm install
```

### Development

```bash
# Start development server
npm start
```

Your application will be available at http://localhost:3000.

### Building for Production

```bash
# Build for production
npm run build
```

### Linting and Formatting

```bash
# Check for linting issues
npm run lint

# Format code
npm run format

# Fix linting issues automatically
npm run fix
```

## Microfrontend Integration

This application is designed to be integrated with other microfrontends. It exposes components via Module Federation:

- `./App` - The main application component
- `./MailboxComponent` - A standalone mailbox component that can be imported in other applications

### Consuming this Microfrontend

To consume this microfrontend in another application configured with Module Federation:

```js
// In the host application's rspack config
{
  plugins: [
    new ModuleFederationPlugin({
      name: 'host',
      remotes: {
        mailboxApp: 'mailboxApp@http://localhost:3000/remoteEntry.js',
      },
      // ... other config
    }),
  ],
}
```

Then in your host application code:

```jsx
// Dynamically import the remote component
const MailboxComponent = React.lazy(() => import('mailboxApp/MailboxComponent'));

// Use it with Suspense
<React.Suspense fallback={<div>Loading...</div>}>
  <MailboxComponent />
</React.Suspense>
```

## Project Structure

```
mailbox-ui/
├── dist/                 # Build output
├── node_modules/         # Dependencies
├── src/
│   ├── components/       # React components
│   │   ├── MailboxComponent.tsx
│   │   └── MailboxComponent.module.css
│   │   
│   ├── App.tsx           # Main App component
│   │   
│   ├── App.module.css    # Styles for App component
│   │   
│   ├── bootstrap.tsx     # Bootstrap code for module federation
│   │   
│   ├── index.html        # HTML template
│   │   
│   └── index.ts          # Entry point
│   
├── biome.json            # Biome configuration
│   
├── package.json          # Package dependencies and scripts
│   
├── README.md             # Project documentation
│   
├── rspack.config.js      # rspack configuration
│   
└── tsconfig.json         # TypeScript configuration
```
