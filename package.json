{"name": "mailbox-ui", "version": "1.0.0", "description": "Microfrontend mailbox UI application", "main": "index.js", "scripts": {"start": "rspack serve -c rspack.config.js", "start:fast": "cross-env SKIP_TYPECHECK=true rspack serve -c rspack.config.js", "build": "rspack build", "upload": "node scripts/s3Upload.js", "build-upload": "node scripts/buildAndUpload.js", "analyze": "rspack build --json stats.json", "build:fast": "cross-env SKIP_TYPECHECK=true rspack build", "lint": "biome check .", "format": "biome format .", "fix": "biome check --apply ."}, "dependencies": {"@klenty/klenty-ui": "^1.4.8-alpha-001", "node-polyfill-webpack-plugin": "^4.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.22.3"}, "devDependencies": {"@biomejs/biome": "1.4.1", "@rsdoctor/cli": "^1.1.2", "@rsdoctor/rspack-plugin": "^1.1.2", "@rspack/cli": "^1.3.9", "@rspack/core": "^1.3.9", "@rspack/plugin-react-refresh": "^1.4.3", "@types/react": "^18.2.41", "@types/react-dom": "^18.2.17", "aws-sdk": "^2.1692.0", "clean-webpack-plugin": "^4.0.0", "crypto-browserify": "^3.12.1", "css-loader": "^6.8.1", "eslint-rspack-plugin": "^4.2.1", "fork-ts-checker-webpack-plugin": "^9.1.0", "html-webpack-plugin": "^5.5.3", "postcss": "^8.4.31", "postcss-loader": "^7.3.3", "postcss-modules-values": "^4.0.0", "stream-browserify": "^3.0.0", "style-loader": "^3.3.3", "typescript": "^5.3.3", "webpack-merge": "^6.0.1"}}