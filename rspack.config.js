const path = require("path");
const { ModuleFederationPlugin } = require("@rspack/core").container;
const { DefinePlugin, HtmlRspackPlugin } = require("@rspack/core");
const { CleanWebpackPlugin } = require("clean-webpack-plugin");
const ForkTsCheckerWebpackPlugin = require("fork-ts-checker-webpack-plugin");
const NodePolyfillPlugin = require("node-polyfill-webpack-plugin");
const ESLintPlugin = require("eslint-rspack-plugin");
const pkg = require("./package.json");
// Check for React JSX runtime support
// This determines if we need to import React in JSX files or if we can use the new JSX transform
const hasJsxRuntime = (() => {
  if (process.env.DISABLE_NEW_JSX_TRANSFORM === "true") return false;

  try {
    require.resolve("react/jsx-runtime");
    return true;
  } catch {
    return false;
  }
})();

module.exports = () => {
  const isProduction = process.env.NODE_ENV === "production";
  const PORT = process.env.PORT || 9000;

  const PUBLIC_URL = process.env.PUBLIC_URL || `http://localhost:${PORT}/`;

  return {
    entry: "./src/App.tsx",
    mode: isProduction ? "production" : "development",
    output: {
      path: path.resolve(__dirname, "build"),
      filename: isProduction ? "js/[name].[contenthash].js" : "js/[name].js",
      chunkFilename: isProduction
        ? "js/[name].[contenthash].chunk.js"
        : "js/[name].chunk.js",
      assetModuleFilename: "assets/[name].[hash:3][ext]",
      publicPath: PUBLIC_URL,
    },
    devtool: false,
    module: {
      rules: [
        {
          test: /\.(ts|tsx)$/,
          use: {
            loader: "builtin:swc-loader",
            options: {
              jsc: {
                parser: { syntax: "typescript", tsx: true },
                transform: { react: { runtime: "automatic" } },
              },
            },
          },
        },
        {
          test: /\.module\.css$/,
          use: [
            { loader: "style-loader" },
            {
              loader: "css-loader",
              options: {
                modules: {
                  localIdentName: "[name]_[local]-[hash:base64:5]"
                }
              }
            }
          ]
        },
        {
          test: /\.css$/,
          exclude: /\.module\.css$/,
          use: [
            { loader: "style-loader" },
            { loader: "css-loader" }
          ]
        },
        {
          test: /\.(png|jpg|jpeg|gif|mp3)$/i,
          type: "asset/resource",
          generator: {
            filename: "assets/images/[name].[hash:3][ext]",
          },
        },
        {
          test: /\.(woff|woff2|eot|ttf|otf)$/i,
          type: "asset/resource",
          generator: {
            filename: "assets/fonts/[name].[hash:3][ext]",
          },
        },
        {
          test: /\.svg$/i,
          issuer: /\.[jt]sx?$/,
          use: ["@svgr/webpack"],
        },
      ],
    },
    resolve: {
      extensions: [".tsx", ".ts", ".js", ".jsx", ".css"],
      alias: {
        components: path.resolve(__dirname, "src/components/"),
        utils: path.resolve(__dirname, "src/utils/"),
        assets: path.resolve(__dirname, "src/assets/"),
        "react/jsx-dev-runtime": require.resolve("react/jsx-dev-runtime"),
        "react/jsx-runtime": require.resolve("react/jsx-runtime"),
      },
    },
    plugins: [
      // Module Federation
      new ModuleFederationPlugin({
        name: "mailBox",
        filename: "remoteEntry.js",
        exposes: {
          "./mailBoxApp": "./src/App.tsx",
        },
        shared: {
          // react: {
          //   singleton: true,
          //   requiredVersion: pkg.dependencies["react"],
          //   eager: true,
          //   shareScope: "default"
          // },
          // "react-dom": {
          //   singleton: true,
          //   requiredVersion: pkg.dependencies["react-dom"],
          //   eager: true,
          //   shareScope: "default"
          // },
          // "react-router-dom": {
          //   singleton: true,
          //   requiredVersion: pkg.dependencies["react-router-dom"],
          //   eager: true,
          //   shareScope: "default"
          // },
          "@klenty/klenty-ui": {
            singleton: true,
            requiredVersion: pkg.dependencies["@klenty/klenty-ui"],
            eager: true,
            shareScope: "default",
          },
        },
      }),
      new NodePolyfillPlugin(),
      new CleanWebpackPlugin(),
      new HtmlRspackPlugin({
        template: "./public/index.html",
        templateParameters: { PUBLIC_URL },
      }),

      // new ESLintPlugin({
      //   extensions: ["js", "mjs", "jsx", "ts", "tsx"],
      //   // formatter: require.resolve("react-dev-utils/eslintFormatter"),
      //   eslintPath: require.resolve("eslint"),
      //   // TODO CHANGE IT TO TRUE WHEN WE ARE READY TO DEPLOY
      //   failOnError:false,
      //   cache: true,
      //   resolvePluginsRelativeTo: __dirname,
      //   baseConfig: {
      //     // extends: [require.resolve("eslint-config-react-app/base")],
      //     rules: {
      //       ...(!hasJsxRuntime && {
      //         "react/react-in-jsx-scope": "error",
      //       }),
      //     },
      //   },
      // }),

      // new ForkTsCheckerWebpackPlugin({
      //   async: !isProduction,
      //   typescript: {
      //     configFile: path.resolve(__dirname, "./tsconfig.json"),
      //     mode: "write-references",
      //     diagnosticOptions: {
      //       semantic: true,
      //       syntactic: true,
      //     },
      //     memoryLimit: 4096,
      //     profile: true,
      //   },
      //   issue: {
      //     include: [{ file: "src/**/*.{ts,tsx}" }], // Include these files for checking
      //     exclude: [
      //       { file: "node_modules/**/*.{ts,tsx}" }, // Exclude node_modules
      //       { file: "**/*.d.ts" }, // Exclude declaration files
      //     ],
      //   },
      // }),
      new DefinePlugin({
        "process.env": {
          NODE_ENV: JSON.stringify(process.env.NODE_ENV),
          REACT_APP_AUTH_BASE_URL: JSON.stringify(
            process.env.REACT_APP_AUTH_BASE_URL
          ),
          REACT_APP_APPLICATION_BASE_URL: JSON.stringify(
            process.env.REACT_APP_APPLICATION_BASE_URL
          ),
        },
      }),
    ],
    devServer: {
      static: {
        directory: path.join(__dirname, "build"),
      },
      historyApiFallback: true,
      port: PORT,
      hot: isProduction ? false : true,
      devMiddleware: {
        writeToDisk: true,
      },
      proxy: [
        {
          context: ['/api',"/NEW","/auth"],
          target: `http://localhost:9095/` || 'https://test-auth-randyorton.test.pods.kl-infra.com/',
          changeOrigin: true,
          secure: false,
          bypass: function (req) {
            // let webpack handle static assets
            if (req.headers.accept && req.headers.accept.includes("html")) {
              return "/index.html";
            }
            // if (req.url.includes('main.js')) {
            //   return "http://localhost:8082/main.js"; // Let webpack-dev-server handle main.js
            // }
          },
        },
      ],
      open: {
        target: ["/"],
      },
    },
  };
};
