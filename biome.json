{"$schema": "https://biomejs.dev/schemas/1.4.1/schema.json", "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "correctness": {"noUnusedVariables": "error"}, "performance": {"noDelete": "error"}, "style": {"noNegationElse": "error", "useShorthandArrayType": "error"}, "suspicious": {"noExplicitAny": "error", "noConsoleLog": "warn"}}}, "formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2, "lineWidth": 100}, "javascript": {"formatter": {"quoteStyle": "single", "trailingComma": "es5", "semicolons": "always"}}}